import React, { useState, useRef } from 'react';
import { Head, router } from '@inertiajs/react';
import { motion } from 'framer-motion';
import QRCode from 'qrcode';
import {
  CheckCircleIcon,
  CalendarIcon,
  MapPinIcon,
  TicketIcon,
  ArrowDownTrayIcon,
  EnvelopeIcon,
  PrinterIcon,
  QrCodeIcon,
  ShareIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import TicketLayout from '../../Layouts/TicketLayout';
import SuccessAlert from '../../Components/Notifications/SuccessAlert';
import ErrorAlert from '../../Components/Notifications/ErrorAlert';

const OrderSuccess = ({ order }) => {
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [showQrCode, setShowQrCode] = useState(false);
  const [showEmailInput, setShowEmailInput] = useState(false);
  const [emailAddress, setEmailAddress] = useState('');
  const [isEmailSending, setIsEmailSending] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const ticketRef = useRef();

  // Generate QR code
  const generateQrCode = async () => {
    try {
      const qrData = order.qr_code || order.order_number;
      const url = await QRCode.toDataURL(qrData, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeUrl(url);
    } catch (error) {
      console.error('Error generating QR code:', error);
      setAlertMessage('Failed to generate QR code. Please try again.');
      setShowErrorAlert(true);
    }
  };

  // Toggle QR code visibility
  const toggleQrCode = async () => {
    if (showQrCode) {
      setShowQrCode(false);
    } else {
      if (!qrCodeUrl) {
        await generateQrCode();
      }
      setShowQrCode(true);
    }
  };

  const formatPrice = (price) => {
    return `₦${Number(price).toLocaleString()}`;
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (time) => {
    return new Date(`2000-01-01 ${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const handleDownload = () => {
    // Show success message
    setAlertMessage('🎫 Generating your PDF ticket... Download will start automatically!');
    setShowSuccessAlert(true);

    // Trigger the PDF download
    window.location.href = `/orders/${order.order_number}/download`;
  };

  const handlePrint = () => {
    window.print();
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `Ticket for ${order.event.title}`,
        text: `I'm attending ${order.event.title}!`,
        url: window.location.href,
      });
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      setAlertMessage('Link copied to clipboard!');
      setShowSuccessAlert(true);
    }
  };

  const handleEmailSend = async () => {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailAddress.trim()) {
      setAlertMessage('Please enter an email address.');
      setShowErrorAlert(true);
      return;
    }

    if (!emailRegex.test(emailAddress.trim())) {
      setAlertMessage('Please enter a valid email address.');
      setShowErrorAlert(true);
      return;
    }

    setIsEmailSending(true);

    try {
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

      if (!csrfToken) {
        throw new Error('Security token not found. Please refresh the page and try again.');
      }

      const response = await fetch(`/orders/${order.order_number}/send-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-TOKEN': csrfToken,
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify({
          email: emailAddress.trim(),
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setAlertMessage(`🎉 Ticket sent successfully to ${emailAddress}!`);
        setShowSuccessAlert(true);
        setShowEmailInput(false);
        setEmailAddress('');
      } else {
        throw new Error(data.message || 'Failed to send email');
      }
    } catch (error) {
      console.error('Error sending email:', error);
      setAlertMessage(error.message || 'Failed to send email. Please try again.');
      setShowErrorAlert(true);
    } finally {
      setIsEmailSending(false);
    }
  };

  const handleBackToEvents = () => {
    router.visit('/events');
  };

  return (
    <TicketLayout event={order.event}>
      <Head title={`Order Confirmation - ${order.event.title}`} />
      
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Success Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-8"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-gradient-to-r from-green-400 to-green-600 mb-6 shadow-lg"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.4, type: "spring", stiffness: 300 }}
              >
                <CheckCircleIcon className="h-10 w-10 text-white" />
              </motion.div>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-3"
            >
              Payment Successful! 🎉
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="text-lg text-gray-600"
            >
              Your tickets for <span className="font-semibold text-indigo-600">{order.event.title}</span> are ready
            </motion.p>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="mt-4 inline-flex items-center px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm font-medium"
            >
              ✓ Order #{order.order_number} confirmed
            </motion.div>
          </motion.div>

          {/* Order Summary Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white rounded-xl shadow-lg overflow-hidden mb-8"
            ref={ticketRef}
          >
            {/* Event Banner Header */}
            <div className="relative h-48 bg-gradient-to-r from-indigo-600 to-purple-600">
              {order.event.banner_image ? (
                <div className="relative h-full">
                  <img
                    src={(() => {
                      const bannerUrl = order.event.banner_image;
                      // Handle different URL patterns
                      if (bannerUrl.startsWith('http')) {
                        return bannerUrl; // Already absolute URL
                      } else if (bannerUrl.startsWith('/storage/')) {
                        return bannerUrl; // Already has /storage/ prefix
                      } else if (bannerUrl.startsWith('storage/')) {
                        return `/${bannerUrl}`; // Add leading slash
                      } else {
                        return `/storage/${bannerUrl.replace(/^\/+/, '')}`; // Add /storage/ prefix
                      }
                    })()}
                    alt={order.event.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      // Show gradient background instead
                      const parent = e.target.parentElement;
                      parent.innerHTML = '<div class="h-full bg-gradient-to-r from-indigo-600 to-purple-600"></div>';
                    }}
                  />
                  {/* Lighter overlay for better visibility */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                </div>
              ) : (
                <div className="h-full bg-gradient-to-r from-indigo-600 to-purple-600"></div>
              )}

              {/* Event Info Overlay */}
              <div className="absolute inset-0 flex items-end">
                <div className="w-full p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-2xl font-bold mb-1">{order.event.title}</h2>
                      <p className="text-white text-opacity-90">Order #{order.order_number}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">{formatPrice(order.total_amount)}</div>
                      <div className="text-white text-opacity-90 text-sm">Total Paid</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Event Details */}
            <div className="p-6 border-b border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-start space-x-3">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mt-1" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Date & Time</p>
                    <p className="text-sm text-gray-600">
                      {formatDate(order.event.event_date)}
                    </p>
                    <p className="text-sm text-gray-600">
                      {formatTime(order.event.event_time)}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <MapPinIcon className="h-5 w-5 text-gray-400 mt-1" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Venue</p>
                    <p className="text-sm text-gray-600">{order.event.venue}</p>
                    <p className="text-sm text-gray-600">
                      {order.event.city}, {order.event.state}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Ticket Details */}
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <TicketIcon className="h-5 w-5 mr-2" />
                Your Tickets
              </h3>
              <div className="space-y-3">
                {order.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                    <div>
                      <p className="font-medium text-gray-900">{item.ticket.name}</p>
                      <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">{formatPrice(item.total_price)}</p>
                      <p className="text-sm text-gray-600">{formatPrice(item.unit_price)} each</p>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* QR Code Section */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-md font-semibold text-gray-900">Entry QR Code</h4>
                  <button
                    onClick={toggleQrCode}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <QrCodeIcon className="h-4 w-4 mr-2" />
                    {showQrCode ? 'Hide QR Code' : 'Show QR Code'}
                  </button>
                </div>
                
                {showQrCode && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className="flex justify-center"
                  >
                    {qrCodeUrl ? (
                      <img src={qrCodeUrl} alt="QR Code" className="border border-gray-200 rounded-lg" />
                    ) : (
                      <div className="border border-gray-200 rounded-lg p-6 bg-gray-50 text-center">
                        <div className="text-sm text-gray-600 mb-2">Entry Code:</div>
                        <div className="font-mono text-lg font-bold text-gray-900 bg-white px-4 py-2 rounded border-2 border-indigo-500">
                          {order.qr_code || order.order_number}
                        </div>
                        <div className="text-xs text-gray-500 mt-2">Present this code at the entrance</div>
                      </div>
                    )}
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8"
          >
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleDownload}
              className="flex items-center justify-center px-4 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <ArrowDownTrayIcon className="h-5 w-5 mr-2" />
              Download PDF
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setShowEmailInput(!showEmailInput)}
              className="flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <EnvelopeIcon className="h-5 w-5 mr-2" />
              Send to Email
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handlePrint}
              className="flex items-center justify-center px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <PrinterIcon className="h-5 w-5 mr-2" />
              Print Ticket
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleShare}
              className="flex items-center justify-center px-4 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <ShareIcon className="h-5 w-5 mr-2" />
              Share Event
            </motion.button>
          </motion.div>

          {/* Email Input Section */}
          {showEmailInput && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100"
            >
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    <EnvelopeIcon className="h-5 w-5 mr-2 text-indigo-600" />
                    Send Ticket to Email
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Enter an email address to receive a copy of your ticket
                  </p>
                </div>
                <button
                  onClick={() => setShowEmailInput(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label htmlFor="email-input" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address
                  </label>
                  <input
                    id="email-input"
                    type="email"
                    value={emailAddress}
                    onChange={(e) => setEmailAddress(e.target.value)}
                    placeholder="Enter email address (e.g., <EMAIL>)"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    disabled={isEmailSending}
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setShowEmailInput(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                    disabled={isEmailSending}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleEmailSend}
                    disabled={isEmailSending || !emailAddress.trim()}
                    className="px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center"
                  >
                    {isEmailSending ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      <>
                        <EnvelopeIcon className="h-4 w-4 mr-2" />
                        Send Ticket
                      </>
                    )}
                  </button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Back to Events Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-center"
          >
            <button
              onClick={handleBackToEvents}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Discover More Events
            </button>
          </motion.div>
        </div>
      </div>

      {/* Success Alert */}
      <SuccessAlert
        show={showSuccessAlert}
        onClose={() => setShowSuccessAlert(false)}
        title="Success!"
        message={alertMessage}
      />

      {/* Error Alert */}
      <ErrorAlert
        show={showErrorAlert}
        onClose={() => setShowErrorAlert(false)}
        title="Error!"
        message={alertMessage}
      />
    </TicketLayout>
  );
};

export default OrderSuccess;
