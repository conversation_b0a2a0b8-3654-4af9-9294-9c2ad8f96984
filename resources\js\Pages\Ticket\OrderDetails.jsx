import React, { useState, useRef } from 'react';
import { Head } from '@inertiajs/react';
import { motion } from 'framer-motion';
import QRCode from 'qrcode';
import {
  CheckCircleIcon,
  CalendarIcon,
  MapPinIcon,
  TicketIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  PrinterIcon,
  QrCodeIcon,
} from '@heroicons/react/24/outline';
import TicketLayout from '../../Layouts/TicketLayout';

const OrderDetails = ({ order }) => {
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [showQrCode, setShowQrCode] = useState(false);
  const ticketRef = useRef();

  // Generate QR code
  const generateQrCode = async () => {
    try {
      const qrData = order.qr_code || order.order_number;
      const url = await QRCode.toDataURL(qrData, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeUrl(url);
      setShowQrCode(true);
    } catch (error) {
      console.error('Error generating QR code:', error);
    }
  };

  const formatPrice = (price) => {
    return `₦${Number(price).toLocaleString()}`;
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (time) => {
    return new Date(`2000-01-01 ${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const handleDownload = () => {
    // This would trigger the download endpoint
    window.open(`/orders/${order.order_number}/download`, '_blank');
  };

  const handlePrint = () => {
    window.print();
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `Ticket for ${order.event.title}`,
        text: `I'm attending ${order.event.title}!`,
        url: window.location.href,
      });
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  return (
    <TicketLayout>
      <Head title={`Order ${order.order_number} - ${order.event.title}`} />
      
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Success Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-8"
          >
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
            <p className="text-gray-600">
              Your tickets for {order.event.title} are ready
            </p>
          </motion.div>

          {/* Ticket */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            ref={ticketRef}
            className="bg-white rounded-lg shadow-lg overflow-hidden mb-8"
          >
            {/* Ticket Header */}
            <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">{order.event.title}</h2>
                  <div className="flex items-center text-indigo-100">
                    <CalendarIcon className="h-5 w-5 mr-2" />
                    {formatDate(order.event.event_date)} at {formatTime(order.event.event_time)}
                  </div>
                  <div className="flex items-center text-indigo-100 mt-1">
                    <MapPinIcon className="h-5 w-5 mr-2" />
                    {order.event.venue}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-indigo-100">Order #</div>
                  <div className="font-mono text-lg">{order.order_number}</div>
                </div>
              </div>
            </div>

            {/* Ticket Body */}
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Order Details */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Details</h3>
                  
                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Buyer Name:</span>
                      <span className="font-medium">{order.buyer_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span className="font-medium">{order.buyer_email}</span>
                    </div>
                    {order.buyer_phone && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Phone:</span>
                        <span className="font-medium">{order.buyer_phone}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600">Payment Status:</span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <CheckCircleIcon className="h-3 w-3 mr-1" />
                        Paid
                      </span>
                    </div>
                  </div>

                  {/* Tickets */}
                  <h4 className="font-medium text-gray-900 mb-3">Tickets</h4>
                  <div className="space-y-2 mb-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-medium text-gray-900">{item.ticket.name}</div>
                          <div className="text-sm text-gray-600">Quantity: {item.quantity}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{formatPrice(item.total_price)}</div>
                          <div className="text-sm text-gray-600">{formatPrice(item.unit_price)} each</div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Total */}
                  <div className="border-t pt-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600">Subtotal:</span>
                      <span>{formatPrice(order.subtotal)}</span>
                    </div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-gray-600">Platform Fee:</span>
                      <span>{formatPrice(order.platform_fee)}</span>
                    </div>
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total Paid:</span>
                      <span>{formatPrice(order.total_amount)}</span>
                    </div>
                  </div>
                </div>

                {/* QR Code Section */}
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Entry QR Code</h3>
                  
                  {!showQrCode ? (
                    <button
                      onClick={generateQrCode}
                      className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                      <QrCodeIcon className="h-5 w-5 mr-2" />
                      Generate QR Code
                    </button>
                  ) : (
                    <div>
                      <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                        <img src={qrCodeUrl} alt="QR Code" className="w-48 h-48" />
                      </div>
                      <p className="text-sm text-gray-600 mt-2">
                        Show this QR code at the event entrance
                      </p>
                    </div>
                  )}

                  {/* Event Info */}
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Event Information</h4>
                    <div className="text-sm text-blue-800 space-y-1">
                      <div>{order.event.venue}</div>
                      <div>{order.event.address}</div>
                      <div>{order.event.city}, {order.event.state}</div>
                      {order.event.contact_email && (
                        <div>Contact: {order.event.contact_email}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="flex flex-wrap justify-center gap-4"
          >
            <button
              onClick={handleDownload}
              className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              <ArrowDownTrayIcon className="h-5 w-5 mr-2" />
              Download Ticket
            </button>
            
            <button
              onClick={handlePrint}
              className="inline-flex items-center px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <PrinterIcon className="h-5 w-5 mr-2" />
              Print Ticket
            </button>
            
            <button
              onClick={handleShare}
              className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <ShareIcon className="h-5 w-5 mr-2" />
              Share
            </button>
          </motion.div>

          {/* Important Notes */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6"
          >
            <h3 className="font-medium text-yellow-900 mb-2">Important Notes</h3>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• Please arrive at least 30 minutes before the event starts</li>
              <li>• Bring a valid ID that matches the name on your ticket</li>
              <li>• Screenshots of QR codes are acceptable for entry</li>
              <li>• Contact the organizer if you have any questions about the event</li>
              <li>• Tickets are non-transferable and non-refundable unless stated otherwise</li>
            </ul>
          </motion.div>
        </div>
      </div>
    </TicketLayout>
  );
};

export default OrderDetails;
