<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Withdrawal;
use App\Models\User;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Withdrawal>
 */
class WithdrawalFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'amount' => $this->faker->numberBetween(5000, 100000),
            'bank_name' => $this->faker->randomElement(['First Bank', 'GTBank', 'Access Bank', 'UBA', 'Zenith Bank']),
            'account_number' => $this->faker->numerify('##########'),
            'account_name' => $this->faker->name,
            'reference_number' => 'WD' . strtoupper(Str::random(8)),
            'status' => 'pending',
            'admin_notes' => null,
            'processed_at' => null,
        ];
    }

    /**
     * Indicate that the withdrawal is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'processed_at' => now(),
            'admin_notes' => 'Withdrawal approved',
        ]);
    }

    /**
     * Indicate that the withdrawal is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'processed_at' => now(),
            'admin_notes' => 'Withdrawal rejected due to insufficient funds',
        ]);
    }

    /**
     * Indicate that the withdrawal is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'processed_at' => now(),
            'admin_notes' => 'Withdrawal completed successfully',
        ]);
    }

    /**
     * Set a specific amount for the withdrawal.
     */
    public function withAmount(int $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amount,
        ]);
    }
}
