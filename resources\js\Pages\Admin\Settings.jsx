import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { motion } from 'framer-motion';
import { 
  UserPlusIcon, 
  UsersIcon, 
  CogIcon, 
  ShieldCheckIcon,
  EyeIcon,
  EyeSlashIcon,
  TrashIcon,
  ClockIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';
import AdminLayout from '../../Layouts/AdminLayout';

const Settings = ({ admin, admins = [] }) => {
  const [activeTab, setActiveTab] = useState('admins');
  const [showPassword, setShowPassword] = useState(false);

  const { data: adminData, setData: setAdminData, post: createAdmin, processing: adminProcessing, errors: adminErrors, reset: resetAdmin } = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
  });

  // Mock admin activity data
  const adminActivities = [
    {
      id: 1,
      admin_name: '<PERSON>',
      action: 'Approved withdrawal',
      details: 'WD-ABC12345 for ₦850,000',
      timestamp: '2024-07-05T10:30:00Z',
      ip_address: '*************'
    },
    {
      id: 2,
      admin_name: 'Sarah Admin',
      action: 'Deactivated user',
      details: 'User: <EMAIL>',
      timestamp: '2024-07-05T09:15:00Z',
      ip_address: '*************'
    },
    {
      id: 3,
      admin_name: 'John Admin',
      action: 'Created new admin',
      details: 'Admin: <EMAIL>',
      timestamp: '2024-07-04T16:45:00Z',
      ip_address: '*************'
    },
    {
      id: 4,
      admin_name: 'Sarah Admin',
      action: 'Rejected withdrawal',
      details: 'WD-DEF67890 - Insufficient documentation',
      timestamp: '2024-07-04T14:20:00Z',
      ip_address: '*************'
    }
  ];

  // Mock admins data if not provided
  const adminsList = admins.length > 0 ? admins : [
    {
      id: 1,
      name: 'TickGet Admin',
      email: '<EMAIL>',
      created_at: '2024-01-01T00:00:00Z',
      last_login: '2024-07-05T08:30:00Z',
      is_current: true
    },
    {
      id: 2,
      name: 'John Admin',
      email: '<EMAIL>',
      created_at: '2024-02-15T10:00:00Z',
      last_login: '2024-07-05T10:30:00Z',
      is_current: false
    },
    {
      id: 3,
      name: 'Sarah Admin',
      email: '<EMAIL>',
      created_at: '2024-03-20T14:30:00Z',
      last_login: '2024-07-05T09:15:00Z',
      is_current: false
    }
  ];

  const tabs = [
    { id: 'admins', name: 'Admin Management', icon: UsersIcon },
    { id: 'activity', name: 'Admin Activity', icon: ClockIcon },
    { id: 'system', name: 'System Settings', icon: CogIcon },
  ];

  const handleCreateAdmin = (e) => {
    e.preventDefault();
    createAdmin('/admin/admins', {
      onSuccess: () => {
        resetAdmin();
      }
    });
  };

  const handleDeleteAdmin = (adminId) => {
    if (confirm('Are you sure you want to delete this admin? This action cannot be undone.')) {
      // This will be connected to the backend
      console.log('Delete admin:', adminId);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <AdminLayout admin={admin}>
      <Head title="Settings - Admin Dashboard" />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Admin Settings
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Manage admin accounts, monitor activity, and configure system settings
            </p>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200`}
                >
                  <tab.icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {/* Admin Management Tab */}
            {activeTab === 'admins' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Create Admin Form */}
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <UserPlusIcon className="h-5 w-5 mr-2" />
                    Create New Admin
                  </h3>
                  <form onSubmit={handleCreateAdmin} className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Full Name
                      </label>
                      <input
                        type="text"
                        id="name"
                        value={adminData.name}
                        onChange={(e) => setAdminData('name', e.target.value)}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        required
                      />
                      {adminErrors.name && <p className="mt-1 text-sm text-red-600">{adminErrors.name}</p>}
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                        Email Address
                      </label>
                      <input
                        type="email"
                        id="email"
                        value={adminData.email}
                        onChange={(e) => setAdminData('email', e.target.value)}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        required
                      />
                      {adminErrors.email && <p className="mt-1 text-sm text-red-600">{adminErrors.email}</p>}
                    </div>

                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        Password
                      </label>
                      <div className="mt-1 relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          id="password"
                          value={adminData.password}
                          onChange={(e) => setAdminData('password', e.target.value)}
                          className="block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                      {adminErrors.password && <p className="mt-1 text-sm text-red-600">{adminErrors.password}</p>}
                    </div>

                    <div>
                      <label htmlFor="password_confirmation" className="block text-sm font-medium text-gray-700">
                        Confirm Password
                      </label>
                      <input
                        type={showPassword ? 'text' : 'password'}
                        id="password_confirmation"
                        value={adminData.password_confirmation}
                        onChange={(e) => setAdminData('password_confirmation', e.target.value)}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                        required
                      />
                      {adminErrors.password_confirmation && <p className="mt-1 text-sm text-red-600">{adminErrors.password_confirmation}</p>}
                    </div>

                    <div className="sm:col-span-2">
                      <button
                        type="submit"
                        disabled={adminProcessing}
                        className="bg-indigo-600 border border-transparent rounded-md shadow-sm py-2 px-4 inline-flex justify-center text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                      >
                        {adminProcessing ? 'Creating...' : 'Create Admin'}
                      </button>
                    </div>
                  </form>
                </div>

                {/* Admins List */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Current Admins</h3>
                  <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table className="min-w-full divide-y divide-gray-300">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Admin
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Last Login
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {adminsList.map((adminUser, index) => (
                          <motion.tr
                            key={adminUser.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  <div className="h-10 w-10 rounded-full bg-indigo-600 flex items-center justify-center">
                                    <ShieldCheckIcon className="h-5 w-5 text-white" />
                                  </div>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900 flex items-center">
                                    {adminUser.name}
                                    {adminUser.is_current && (
                                      <span className="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        You
                                      </span>
                                    )}
                                  </div>
                                  <div className="text-sm text-gray-500">{adminUser.email}</div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatDate(adminUser.created_at)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatDate(adminUser.last_login)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              {!adminUser.is_current && (
                                <button
                                  onClick={() => handleDeleteAdmin(adminUser.id)}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              )}
                            </td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Admin Activity Tab */}
            {activeTab === 'activity' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Admin Activity</h3>
                <div className="flow-root">
                  <ul className="-mb-8">
                    {adminActivities.map((activity, index) => (
                      <motion.li
                        key={activity.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <div className="relative pb-8">
                          {index !== adminActivities.length - 1 && (
                            <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                          )}
                          <div className="relative flex space-x-3">
                            <div>
                              <span className="h-8 w-8 rounded-full bg-indigo-500 flex items-center justify-center ring-8 ring-white">
                                <ShieldCheckIcon className="h-4 w-4 text-white" />
                              </span>
                            </div>
                            <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                              <div>
                                <p className="text-sm text-gray-500">
                                  <span className="font-medium text-gray-900">{activity.admin_name}</span> {activity.action}
                                </p>
                                <p className="text-sm text-gray-600">{activity.details}</p>
                                <p className="text-xs text-gray-400">IP: {activity.ip_address}</p>
                              </div>
                              <div className="text-right text-sm whitespace-nowrap text-gray-500">
                                <time>{formatDate(activity.timestamp)}</time>
                              </div>
                            </div>
                          </div>
                        </div>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            )}

            {/* System Settings Tab */}
            {activeTab === 'system' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                    <ComputerDesktopIcon className="h-5 w-5 mr-2" />
                    System Information
                  </h3>
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">Platform Version</div>
                      <div className="text-lg font-semibold text-gray-900">TickGet v1.0.0</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">Laravel Version</div>
                      <div className="text-lg font-semibold text-gray-900">11.45.1</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">PHP Version</div>
                      <div className="text-lg font-semibold text-gray-900">8.2.12</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">Database</div>
                      <div className="text-lg font-semibold text-gray-900">SQLite</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">Platform Commission</div>
                      <div className="text-lg font-semibold text-gray-900">10%</div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-500">Payment Gateway</div>
                      <div className="text-lg font-semibold text-gray-900">Paystack</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-3">Platform Settings</h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">User Registration</h5>
                        <p className="text-sm text-gray-500">Allow new users to register on the platform</p>
                      </div>
                      <button
                        type="button"
                        className="bg-indigo-600 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        <span className="translate-x-5 pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"></span>
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Event Approval</h5>
                        <p className="text-sm text-gray-500">Require admin approval for new events</p>
                      </div>
                      <button
                        type="button"
                        className="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        <span className="translate-x-0 pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"></span>
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Maintenance Mode</h5>
                        <p className="text-sm text-gray-500">Put the platform in maintenance mode</p>
                      </div>
                      <button
                        type="button"
                        className="bg-gray-200 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        <span className="translate-x-0 pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"></span>
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Settings;
