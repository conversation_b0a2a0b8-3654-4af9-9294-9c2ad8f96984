<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use App\Models\User;
use Inertia\Inertia;

class AuthController extends Controller
{
    /**
     * Show the login form
     */
    public function showLogin()
    {
        return Inertia::render('Guest/Login');
    }

    /**
     * Handle user login request
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($request->only('email', 'password'), $request->boolean('remember'))) {
            $request->session()->regenerate();

            /** @var User $user */
            $user = Auth::user();

            // Ensure only non-admin users can login through regular login
            if ($user->isAdmin()) {
                Auth::logout();
                throw ValidationException::withMessages([
                    'email' => 'Please use the admin login page.',
                ]);
            }

            // Check if user account is active
            if (!$user->is_active) {
                Auth::logout();
                throw ValidationException::withMessages([
                    'email' => 'Your account has been deactivated. Please contact support for assistance.',
                ]);
            }

            return redirect()->intended('/dashboard');
        }

        throw ValidationException::withMessages([
            'email' => 'The provided credentials do not match our records.',
        ]);
    }

    /**
     * Show the registration form
     */
    public function showRegister()
    {
        return Inertia::render('Guest/Register');
    }

    /**
     * Handle registration request
     */
    public function register(Request $request)
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:user,organization',
            'phone' => 'nullable|string|max:20',
            'location' => 'nullable|string|max:255',
        ];

        // Add organization validation only if role is organization
        if ($request->role === 'organization') {
            $rules['organization_name'] = 'required|string|max:255';
            $rules['organization_type'] = 'required|string|max:255';
        }

        $validated = $request->validate($rules);

        $userData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => $validated['role'],
            'phone' => $validated['phone'] ?? null,
            'location' => $validated['location'] ?? null,
            'is_active' => true,
            'email_verified_at' => now(),
        ];

        // Add organization fields only if role is organization
        if ($validated['role'] === 'organization') {
            $userData['organization_name'] = $validated['organization_name'];
            $userData['organization_type'] = $validated['organization_type'];
        }

        $user = User::create($userData);

        Auth::login($user);

        return redirect('/dashboard')->with('success', 'Account created successfully! Welcome to TickGet.');
    }

    /**
     * Show the admin login form
     */
    public function showAdminLogin()
    {
        return Inertia::render('Admin/Login');
    }

    /**
     * Handle admin login request
     */
    public function adminLogin(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($request->only('email', 'password'), $request->boolean('remember'))) {
            $request->session()->regenerate();

            /** @var User $user */
            $user = Auth::user();

            // Ensure only admin users can login through admin login
            if (!$user->isAdmin()) {
                Auth::logout();
                throw ValidationException::withMessages([
                    'email' => 'Access denied. Admin privileges required.',
                ]);
            }

            return redirect()->intended('/admin/dashboard');
        }

        throw ValidationException::withMessages([
            'email' => 'The provided credentials do not match our records.',
        ]);
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }

    /**
     * Handle admin logout request
     */
    public function adminLogout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/admin/login');
    }
}
