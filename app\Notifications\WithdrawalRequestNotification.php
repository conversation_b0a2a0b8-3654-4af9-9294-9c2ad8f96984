<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Withdrawal;

class WithdrawalRequestNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $withdrawal;

    /**
     * Create a new notification instance.
     */
    public function __construct(Withdrawal $withdrawal)
    {
        $this->withdrawal = $withdrawal;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $withdrawal = $this->withdrawal;
        $user = $withdrawal->user;
        
        return (new MailMessage)
            ->subject('New Withdrawal Request - ' . $withdrawal->reference_number)
            ->greeting('Hello Admin!')
            ->line('A new withdrawal request has been submitted and requires your review.')
            ->line('')
            ->line('**Withdrawal Details:**')
            ->line('Reference: ' . $withdrawal->reference_number)
            ->line('Amount: ₦' . number_format($withdrawal->amount, 2))
            ->line('Requested by: ' . $user->name . ' (' . $user->email . ')')
            ->line('Organization: ' . ($user->organization_name ?: 'Individual'))
            ->line('Request Date: ' . $withdrawal->created_at->format('F j, Y \a\t g:i A'))
            ->line('')
            ->line('**Bank Details:**')
            ->line('Bank Name: ' . $withdrawal->bank_name)
            ->line('Account Number: ' . $withdrawal->account_number)
            ->line('Account Name: ' . $withdrawal->account_name)
            ->line('')
            ->action('Review Withdrawal Request', route('admin.withdrawals'))
            ->line('Please review this request and take appropriate action.')
            ->salutation('TickGet Admin System');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'withdrawal_id' => $this->withdrawal->id,
            'reference_number' => $this->withdrawal->reference_number,
            'amount' => $this->withdrawal->amount,
            'user_name' => $this->withdrawal->user->name,
            'user_email' => $this->withdrawal->user->email,
        ];
    }
}
