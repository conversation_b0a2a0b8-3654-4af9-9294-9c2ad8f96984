import { useState, useEffect } from 'react';
import { Head, router } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  QrCodeIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserGroupIcon,
  TicketIcon,
  ClockIcon,
  CalendarIcon,
  MapPinIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentTextIcon,
  EyeIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import AppLayout from '../../Layouts/AppLayout';
import SuccessAlert from '../../Components/Notifications/SuccessAlert';
import ErrorAlert from '../../Components/Notifications/ErrorAlert';

const ManageEvent = ({ user, event, attendees = [], stats = {} }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedAttendee, setSelectedAttendee] = useState(null);
  const [showQrScanner, setShowQrScanner] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [scannedCode, setScannedCode] = useState('');

  // Filter attendees based on search and status
  const filteredAttendees = attendees.filter(attendee => {
    const matchesSearch = attendee.buyer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         attendee.buyer_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         attendee.order_number.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'checked-in' && attendee.checked_in) ||
                         (statusFilter === 'not-checked-in' && !attendee.checked_in);
    
    return matchesSearch && matchesStatus;
  });

  // Format date and time
  const formatDateTime = (date, time) => {
    const eventDate = new Date(`${date}T${time}`);
    return eventDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  // Handle check-in/check-out
  const handleCheckIn = (attendeeId, action) => {
    router.post(`/events/${event.slug}/attendees/${attendeeId}/check-in`, {
      action: action // 'check-in' or 'check-out'
    }, {
      onSuccess: () => {
        setAlertMessage(`Attendee ${action === 'check-in' ? 'checked in' : 'checked out'} successfully!`);
        setShowSuccessAlert(true);
      },
      onError: () => {
        setAlertMessage('Failed to update attendee status. Please try again.');
        setShowErrorAlert(true);
      }
    });
  };

  // Handle QR code scan
  const handleQrScan = () => {
    if (!scannedCode.trim()) {
      setAlertMessage('Please enter a valid ticket code.');
      setShowErrorAlert(true);
      return;
    }

    router.post(`/events/${event.slug}/verify-ticket`, {
      code: scannedCode
    }, {
      onSuccess: () => {
        setAlertMessage('Ticket verified and attendee checked in successfully!');
        setShowSuccessAlert(true);
        setScannedCode('');
        setShowQrScanner(false);
      },
      onError: (errors) => {
        setAlertMessage(errors.code || 'Invalid ticket code. Please try again.');
        setShowErrorAlert(true);
      }
    });
  };

  return (
    <AppLayout user={user}>
      <Head title={`Manage ${event.title} - TickGet`} />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-indigo-100 rounded-lg">
                  <TicketIcon className="h-6 w-6 text-indigo-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">{event.title}</h1>
                  <p className="text-gray-600">Event Management Dashboard</p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="flex items-center text-sm text-gray-600">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {formatDateTime(event.event_date, event.event_time)}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <MapPinIcon className="h-4 w-4 mr-2" />
                  {event.venue}, {event.city}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <UserGroupIcon className="h-4 w-4 mr-2" />
                  {stats.total_attendees || 0} Total Attendees
                </div>
              </div>
            </div>
            
            <div className="flex gap-3">
              <motion.button
                onClick={() => setShowQrScanner(!showQrScanner)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200"
              >
                <QrCodeIcon className="h-5 w-5 mr-2" />
                Scan Ticket
              </motion.button>
              
              <motion.button
                onClick={() => window.open(`/event-details/${event.slug}`, '_blank')}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <EyeIcon className="h-5 w-5 mr-2" />
                View Event
              </motion.button>
            </div>
          </div>
        </div>

        {/* QR Scanner Section */}
        {showQrScanner && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Ticket Verification</h3>
            <div className="flex gap-3">
              <input
                type="text"
                value={scannedCode}
                onChange={(e) => setScannedCode(e.target.value)}
                placeholder="Enter ticket code or scan QR code"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                onKeyPress={(e) => e.key === 'Enter' && handleQrScan()}
              />
              <motion.button
                onClick={handleQrScan}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
              >
                Verify
              </motion.button>
            </div>
          </motion.div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
          >
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <UserGroupIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Attendees</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_attendees || 0}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
          >
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <CheckCircleIcon className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Checked In</p>
                <p className="text-2xl font-bold text-gray-900">{stats.checked_in || 0}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
          >
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <ClockIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pending || 0}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
          >
            <div className="flex items-center">
              <div className="p-3 bg-indigo-100 rounded-lg">
                <TicketIcon className="h-6 w-6 text-indigo-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Check-in Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.total_attendees > 0 ? Math.round((stats.checked_in / stats.total_attendees) * 100) : 0}%
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name, email, or order number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
            </div>
            
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="all">All Attendees</option>
                <option value="checked-in">Checked In</option>
                <option value="not-checked-in">Not Checked In</option>
              </select>
            </div>
          </div>
        </div>

        {/* Attendees List */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              Attendees ({filteredAttendees.length})
            </h3>
          </div>
          
          <div className="overflow-x-auto">
            {filteredAttendees.length === 0 ? (
              <div className="text-center py-12">
                <UserGroupIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No attendees found</h3>
                <p className="text-gray-600">
                  {searchTerm || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filter criteria.' 
                    : 'No one has purchased tickets for this event yet.'}
                </p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Attendee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Order Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredAttendees.map((attendee, index) => (
                    <motion.tr
                      key={attendee.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{attendee.buyer_name}</div>
                          <div className="text-sm text-gray-500">{attendee.buyer_email}</div>
                          {attendee.buyer_phone && (
                            <div className="text-sm text-gray-500">{attendee.buyer_phone}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">#{attendee.order_number}</div>
                          <div className="text-sm text-gray-500">
                            {attendee.tickets_count} ticket{attendee.tickets_count !== 1 ? 's' : ''}
                          </div>
                          <div className="text-sm text-gray-500">
                            ₦{Number(attendee.total_amount).toLocaleString()}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          attendee.checked_in
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {attendee.checked_in ? (
                            <>
                              <CheckCircleIcon className="h-3 w-3 mr-1" />
                              Checked In
                            </>
                          ) : (
                            <>
                              <ClockIcon className="h-3 w-3 mr-1" />
                              Pending
                            </>
                          )}
                        </span>
                        {attendee.checked_in_at && (
                          <div className="text-xs text-gray-500 mt-1">
                            {new Date(attendee.checked_in_at).toLocaleString()}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          {attendee.checked_in ? (
                            <motion.button
                              onClick={() => handleCheckIn(attendee.id, 'check-out')}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              className="inline-flex items-center px-3 py-1.5 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100"
                            >
                              <XMarkIcon className="h-4 w-4 mr-1" />
                              Check Out
                            </motion.button>
                          ) : (
                            <motion.button
                              onClick={() => handleCheckIn(attendee.id, 'check-in')}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              className="inline-flex items-center px-3 py-1.5 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100"
                            >
                              <CheckIcon className="h-4 w-4 mr-1" />
                              Check In
                            </motion.button>
                          )}
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* Success Alert */}
        <SuccessAlert
          show={showSuccessAlert}
          onClose={() => setShowSuccessAlert(false)}
          title="Success!"
          message={alertMessage}
        />

        {/* Error Alert */}
        <ErrorAlert
          show={showErrorAlert}
          onClose={() => setShowErrorAlert(false)}
          title="Error!"
          message={alertMessage}
        />
      </div>
    </AppLayout>
  );
};

export default ManageEvent;
