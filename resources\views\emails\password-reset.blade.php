<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - TickGet</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }
        
        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 30px;
            line-height: 1.7;
        }
        
        .token-container {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }
        
        .token-label {
            font-size: 14px;
            color: #718096;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }
        
        .token {
            font-size: 36px;
            font-weight: 800;
            color: #667eea;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
        }
        
        .token-note {
            font-size: 12px;
            color: #a0aec0;
            margin-top: 15px;
            font-style: italic;
        }
        
        .warning {
            background-color: #fef5e7;
            border-left: 4px solid #f6ad55;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .warning-title {
            font-weight: 600;
            color: #c05621;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .warning-icon {
            margin-right: 8px;
            font-size: 18px;
        }
        
        .warning-text {
            font-size: 14px;
            color: #744210;
            line-height: 1.5;
        }
        
        .instructions {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .instructions-title {
            font-weight: 600;
            color: #22543d;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .instructions ol {
            color: #2f855a;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .footer {
            background-color: #f7fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            font-size: 14px;
            color: #718096;
            margin-bottom: 15px;
        }
        
        .footer-links {
            font-size: 12px;
            color: #a0aec0;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 30px 20px;
            }
            
            .token {
                font-size: 28px;
                letter-spacing: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="images/Tickgetlogo.png" alt="TickGet Logo">
            </div>
            <h1>TickGet</h1>
            <p>Your Trusted Event Ticketing Platform</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello {{ $user->name ?? 'User' }}!
            </div>
            
            <div class="message">
                We received a request to reset your password for your TickGet account. To proceed with resetting your password, please use the verification code below.
            </div>
            
            <div class="token-container">
                <div class="token-label">Your Reset Code</div>
                <div class="token">{{ $token }}</div>
                <div class="token-note">This code expires in 30 minutes</div>
            </div>
            
            <div class="instructions">
                <div class="instructions-title">How to reset your password:</div>
                <ol>
                    <li>Go back to the password reset page</li>
                    <li>Enter your email address</li>
                    <li>Enter the 7-character code above</li>
                    <li>Create your new password</li>
                    <li>Confirm your new password</li>
                </ol>
            </div>
            
            <div class="warning">
                <div class="warning-title">
                    <span class="warning-icon">⚠️</span>
                    Security Notice
                </div>
                <div class="warning-text">
                    If you didn't request this password reset, please ignore this email. Your account remains secure. For additional security, consider changing your password if you suspect unauthorized access.
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">
                This email was sent from TickGet. If you have any questions, please contact our support team.
            </div>
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Contact Support</a>
            </div>
        </div>
    </div>
</body>
</html>
