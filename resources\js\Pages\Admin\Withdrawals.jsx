import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  CreditCardIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  BanknotesIcon,
  UserIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import AdminLayout from '../../Layouts/AdminLayout';
import ConfirmationModal from '../../Components/ConfirmationModal';
import SuccessAlert from '../../Components/SuccessAlert';
import ErrorAlert from '../../Components/ErrorAlert';

const Withdrawals = ({ admin, withdrawals, filters = {} }) => {
  const [filterStatus, setFilterStatus] = useState(filters.status || 'all');
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [showCompleteModal, setShowCompleteModal] = useState(false);
  const [selectedWithdrawal, setSelectedWithdrawal] = useState(null);
  const [rejectReason, setRejectReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Handle filter changes
  const handleFilterChange = (status) => {
    setFilterStatus(status);
    const params = new URLSearchParams();
    if (status !== 'all') params.append('status', status);

    window.location.href = `/admin/withdrawals?${params.toString()}`;
  };

  const handleApprove = (withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setShowApproveModal(true);
  };

  const handleReject = (withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setRejectReason('');
    setShowRejectModal(true);
  };

  const handleComplete = (withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setShowCompleteModal(true);
  };

  const confirmApprove = async () => {
    setIsLoading(true);
    try {
      console.log('Approving withdrawal:', selectedWithdrawal.id);
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
      console.log('CSRF Token:', csrfToken);

      const response = await fetch(`/admin/withdrawals/${selectedWithdrawal.id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({ admin_notes: 'Approved by admin' })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Approval response:', result);
        setSuccessMessage('Withdrawal approved successfully!');
        setShowSuccessAlert(true);
        setTimeout(() => window.location.reload(), 2000);
      } else {
        const errorData = await response.text();
        console.error('Approval failed:', response.status, errorData);
        throw new Error(`Failed to approve withdrawal: ${response.status}`);
      }
    } catch (error) {
      console.error('Error approving withdrawal:', error);
      setErrorMessage('Failed to approve withdrawal. Please try again.');
      setShowErrorAlert(true);
    } finally {
      setIsLoading(false);
      setShowApproveModal(false);
    }
  };

  const confirmReject = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/admin/withdrawals/${selectedWithdrawal.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ admin_notes: rejectReason })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Rejection response:', result);
        setSuccessMessage('Withdrawal rejected successfully!');
        setShowSuccessAlert(true);
        setTimeout(() => window.location.reload(), 2000);
      } else {
        const errorData = await response.text();
        console.error('Rejection failed:', response.status, errorData);
        throw new Error(`Failed to reject withdrawal: ${response.status}`);
      }
    } catch (error) {
      console.error('Error rejecting withdrawal:', error);
      setErrorMessage('Failed to reject withdrawal. Please try again.');
      setShowErrorAlert(true);
    } finally {
      setIsLoading(false);
      setShowRejectModal(false);
    }
  };

  const confirmComplete = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/admin/withdrawals/${selectedWithdrawal.id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ admin_notes: 'Payment processed successfully' })
      });

      if (response.ok) {
        setSuccessMessage('Withdrawal marked as completed successfully!');
        setShowSuccessAlert(true);
        setTimeout(() => window.location.reload(), 2000);
      } else {
        throw new Error('Failed to complete withdrawal');
      }
    } catch (error) {
      console.error('Error completing withdrawal:', error);
      setErrorMessage('Failed to complete withdrawal. Please try again.');
      setShowErrorAlert(true);
    } finally {
      setIsLoading(false);
      setShowCompleteModal(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      approved: { bg: 'bg-blue-100', text: 'text-blue-800', label: 'Approved' },
      completed: { bg: 'bg-green-100', text: 'text-green-800', label: 'Completed' },
      rejected: { bg: 'bg-red-100', text: 'text-red-800', label: 'Rejected' }
    };

    const config = statusConfig[status];
    return (
      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'Invalid Date';
    }
  };

  return (
    <AdminLayout admin={admin}>
      <Head title="Withdrawals Management - Admin Dashboard" />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Withdrawals Management
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Review and process withdrawal requests from organizers
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-6 w-6 text-yellow-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {withdrawals.data ? withdrawals.data.filter(w => w.status === 'pending').length : 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircleIcon className="h-6 w-6 text-blue-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Approved</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {withdrawals.data ? withdrawals.data.filter(w => w.status === 'approved').length : 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BanknotesIcon className="h-6 w-6 text-green-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Completed</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {withdrawals.data ? withdrawals.data.filter(w => w.status === 'completed').length : 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCardIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Amount</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      ₦{withdrawals.data ? (withdrawals.data.reduce((sum, w) => sum + parseFloat(w.amount || 0), 0) / 1000000).toFixed(1) : 0}M
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg">
          <div className="p-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Filter by Status</label>
                <select
                  value={filterStatus}
                  onChange={(e) => handleFilterChange(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="all">All Withdrawals</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="completed">Completed</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Withdrawals Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Withdrawal Requests ({withdrawals.data ? withdrawals.data.length : 0})
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reference
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bank Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {withdrawals.data && withdrawals.data.length > 0 ? withdrawals.data.map((withdrawal, index) => (
                  <motion.tr
                    key={withdrawal.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{withdrawal.reference_number || `WD-${withdrawal.id}`}</div>
                      {withdrawal.notes && (
                        <div className="text-xs text-gray-500 mt-1">{withdrawal.notes}</div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                            <UserIcon className="h-4 w-4 text-gray-600" />
                          </div>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{withdrawal.user?.name || 'Unknown User'}</div>
                          <div className="text-sm text-gray-500">{withdrawal.user?.email || 'N/A'}</div>
                          {withdrawal.user?.organization_name && (
                            <div className="text-xs text-gray-400">{withdrawal.user.organization_name}</div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        ₦{Number(withdrawal.amount || 0).toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{withdrawal.bank_name}</div>
                      <div className="text-sm text-gray-500">{withdrawal.account_number}</div>
                      <div className="text-xs text-gray-400">{withdrawal.account_name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(withdrawal.status)}
                      {withdrawal.admin_notes && (
                        <div className="text-xs text-gray-500 mt-1">{withdrawal.admin_notes}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <CalendarIcon className="h-4 w-4 inline mr-1" />
                        {formatDate(withdrawal.created_at)}
                      </div>
                      {withdrawal.processed_at && (
                        <div className="text-xs text-gray-500">
                          Processed: {formatDate(withdrawal.processed_at)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        {withdrawal.status === 'pending' && (
                          <>
                            <button 
                              onClick={() => handleApprove(withdrawal)}
                              className="text-green-600 hover:text-green-900"
                              title="Approve"
                            >
                              <CheckCircleIcon className="h-4 w-4" />
                            </button>
                            <button 
                              onClick={() => handleReject(withdrawal)}
                              className="text-red-600 hover:text-red-900"
                              title="Reject"
                            >
                              <XCircleIcon className="h-4 w-4" />
                            </button>
                          </>
                        )}
                        {withdrawal.status === 'approved' && (
                          <button 
                            onClick={() => handleComplete(withdrawal)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Mark as Completed"
                          >
                            <BanknotesIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </motion.tr>
                )) : (
                  <tr>
                    <td colSpan="7" className="px-6 py-12 text-center">
                      <div className="text-gray-500">
                        <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No withdrawal requests found</h3>
                        <p className="text-gray-600">No withdrawal requests have been submitted yet or match your filter criteria.</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Confirmation Modals */}
      <ConfirmationModal
        isOpen={showApproveModal}
        onClose={() => setShowApproveModal(false)}
        onConfirm={confirmApprove}
        title="Approve Withdrawal"
        message={`Are you sure you want to approve this withdrawal request for ₦${selectedWithdrawal ? Number(selectedWithdrawal.amount).toLocaleString() : 0}?`}
        confirmText="Approve"
        type="success"
        isLoading={isLoading}
      />

      <ConfirmationModal
        isOpen={showRejectModal}
        onClose={() => setShowRejectModal(false)}
        onConfirm={confirmReject}
        title="Reject Withdrawal"
        message={`Please provide a reason for rejecting this withdrawal request for ₦${selectedWithdrawal ? Number(selectedWithdrawal.amount).toLocaleString() : 0}:`}
        confirmText="Reject"
        type="danger"
        isLoading={isLoading}
        showInput={true}
        inputPlaceholder="Enter reason for rejection..."
        inputValue={rejectReason}
        onInputChange={setRejectReason}
      />

      <ConfirmationModal
        isOpen={showCompleteModal}
        onClose={() => setShowCompleteModal(false)}
        onConfirm={confirmComplete}
        title="Complete Withdrawal"
        message={`Are you sure you want to mark this withdrawal as completed? This indicates that the payment has been processed successfully.`}
        confirmText="Mark as Completed"
        type="success"
        isLoading={isLoading}
      />

      {/* Success Alert */}
      <SuccessAlert
        show={showSuccessAlert}
        onClose={() => setShowSuccessAlert(false)}
        message={successMessage}
      />

      {/* Error Alert */}
      <ErrorAlert
        show={showErrorAlert}
        onClose={() => setShowErrorAlert(false)}
        message={errorMessage}
      />
    </AdminLayout>
  );
};

export default Withdrawals;
