<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Notification;
use App\Notifications\WithdrawalRequestNotification;
use App\Notifications\WithdrawalStatusNotification;

class Withdrawal extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'reference_number',
        'amount',
        'bank_name',
        'account_number',
        'account_name',
        'status',
        'processed_at',
        'notes',
        'admin_notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'processed_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($withdrawal) {
            if (empty($withdrawal->reference_number)) {
                $withdrawal->reference_number = 'WD-' . strtoupper(Str::random(10));
            }
        });
    }

    /**
     * Get the user who made this withdrawal request
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Mark withdrawal as approved
     */
    public function approve(?string $adminNotes = null): void
    {
        $this->update([
            'status' => 'approved',
            'processed_at' => now(),
            'admin_notes' => $adminNotes,
        ]);

        // Send notification to user
        $this->user->notify(new WithdrawalStatusNotification($this, 'approved'));
    }

    /**
     * Mark withdrawal as rejected
     */
    public function reject(?string $adminNotes = null): void
    {
        $this->update([
            'status' => 'rejected',
            'processed_at' => now(),
            'admin_notes' => $adminNotes,
        ]);

        // Send notification to user
        $this->user->notify(new WithdrawalStatusNotification($this, 'rejected'));
    }

    /**
     * Mark withdrawal as completed
     */
    public function complete(?string $adminNotes = null): void
    {
        $this->update([
            'status' => 'completed',
            'admin_notes' => $adminNotes,
        ]);

        // Send notification to user
        $this->user->notify(new WithdrawalStatusNotification($this, 'completed'));
    }

    /**
     * Check if withdrawal is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if withdrawal is approved
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if withdrawal is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if withdrawal is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmount(): string
    {
        return '₦' . number_format($this->amount, 2);
    }

    /**
     * Scope for pending withdrawals
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for approved withdrawals
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope for completed withdrawals
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
}
