<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'organizer_id',
        'title',
        'slug',
        'description',
        'short_description',
        'event_date',
        'event_time',
        'end_date',
        'end_time',
        'venue',
        'address',
        'city',
        'state',
        'country',
        'event_type',
        'category',
        'banner_image',
        'gallery_images',
        'terms_conditions',
        'additional_info',
        'contact_email',
        'contact_phone',
        'website_url',
        'social_links',
        'is_active',
        'is_featured',
        'max_attendees',
        'current_attendees',
        'requires_approval',
        'custom_fields',
        'page_customization',
        'seo_title',
        'seo_description',
        'view_count',
    ];

    protected $casts = [
        'event_date' => 'date',
        'end_date' => 'date',
        'event_time' => 'datetime',
        'end_time' => 'datetime',
        'gallery_images' => 'array',
        'social_links' => 'array',
        'custom_fields' => 'array',
        'page_customization' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'requires_approval' => 'boolean',
        'max_attendees' => 'integer',
        'current_attendees' => 'integer',
        'view_count' => 'integer',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            if (empty($event->slug)) {
                $baseSlug = Str::slug($event->title);
                $slug = $baseSlug;
                $counter = 1;

                // Ensure slug is unique
                while (static::where('slug', $slug)->exists()) {
                    $slug = $baseSlug . '-' . $counter;
                    $counter++;
                }

                $event->slug = $slug;
            }
        });
    }

    /**
     * Get the organizer of this event
     */
    public function organizer()
    {
        return $this->belongsTo(User::class, 'organizer_id');
    }

    /**
     * Get tickets for this event
     */
    public function tickets()
    {
        return $this->hasMany(Ticket::class);
    }

    /**
     * Get orders for this event
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get active tickets for this event
     */
    public function activeTickets()
    {
        return $this->hasMany(Ticket::class)->where('is_active', true);
    }

    /**
     * Check if event is expired
     */
    public function isExpired(): bool
    {
        return $this->event_date < now();
    }

    /**
     * Check if event is sold out
     */
    public function isSoldOut(): bool
    {
        return $this->max_attendees && $this->current_attendees >= $this->max_attendees;
    }

    /**
     * Get total revenue for this event
     */
    public function getTotalRevenue()
    {
        return $this->orders()
            ->where('status', 'completed')
            ->sum('total_amount');
    }

    /**
     * Get organizer revenue (90% after platform fee)
     */
    public function getOrganizerRevenue()
    {
        return $this->getTotalRevenue() * 0.9;
    }

    /**
     * Get platform commission (10%)
     */
    public function getPlatformCommission()
    {
        return $this->getTotalRevenue() * 0.1;
    }

    /**
     * Increment view count
     */
    public function incrementViewCount()
    {
        $this->increment('view_count');
    }

    /**
     * Get route key name for model binding
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }
}
