<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Order;

class PaymentSuccessNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $order;

    /**
     * Create a new notification instance.
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $order = $this->order;
        $event = $order->event;
        
        return (new MailMessage)
            ->subject('Payment Successful - Your Tickets are Ready!')
            ->greeting('Great news, ' . $order->buyer_name . '!')
            ->line('Your payment has been processed successfully and your tickets are now ready!')
            ->line('')
            ->line('**Payment Details:**')
            ->line('Order Number: ' . $order->order_number)
            ->line('Payment Reference: ' . $order->payment_reference)
            ->line('Amount Paid: ₦' . number_format($order->total_amount, 2))
            ->line('Payment Date: ' . $order->updated_at->format('F j, Y \a\t g:i A'))
            ->line('')
            ->line('**Event Information:**')
            ->line('Event: ' . $event->title)
            ->line('Date: ' . $event->event_date->format('l, F j, Y'))
            ->line('Time: ' . $event->event_time)
            ->line('Venue: ' . $event->venue)
            ->line('Location: ' . $event->city . ', ' . $event->state)
            ->line('')
            ->action('Download Your Tickets', route('orders.show', $order->order_number))
            ->line('')
            ->line('**Important Reminders:**')
            ->line('• Please arrive at least 30 minutes before the event starts')
            ->line('• Bring a valid ID that matches the name on your ticket')
            ->line('• Your QR code ticket can be shown on your phone or printed')
            ->line('• Keep this email as proof of purchase')
            ->line('')
            ->line('We hope you have an amazing time at the event!')
            ->salutation('Best regards, The TickGet Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'event_title' => $this->order->event->title,
            'payment_reference' => $this->order->payment_reference,
            'total_amount' => $this->order->total_amount,
        ];
    }
}
