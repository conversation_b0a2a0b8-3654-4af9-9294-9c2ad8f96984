<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Order;

class TicketEmailNotification extends Notification
{
    // Removed Queueable trait and ShouldQueue interface to send emails immediately

    protected $order;

    /**
     * Create a new notification instance.
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $order = $this->order;
        $event = $order->event;

        return (new MailMessage)
            ->subject('Your Ticket for ' . $event->title . ' - TickGet')
            ->view('emails.ticket-email', [
                'order' => $order,
                'event' => $event
            ]);
    }

    /**
     * Get ticket details as lines
     */
    protected function getTicketLines(): array
    {
        $lines = [];
        
        foreach ($this->order->items as $item) {
            $lines[] = "• {$item->ticket->name} × {$item->quantity} - ₦" . number_format($item->total_price, 2);
        }
        
        return $lines;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'order_id' => $this->order->id,
            'order_number' => $this->order->order_number,
            'event_title' => $this->order->event->title,
            'total_amount' => $this->order->total_amount,
        ];
    }
}
