<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Event;

class EventStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $event;
    protected $status;
    protected $adminNotes;

    /**
     * Create a new notification instance.
     */
    public function __construct(Event $event, string $status, string $adminNotes = null)
    {
        $this->event = $event;
        $this->status = $status;
        $this->adminNotes = $adminNotes;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $event = $this->event;
        $statusTitle = $this->status === 'active' ? 'Approved' : 'Deactivated';
        
        $message = (new MailMessage)
            ->subject("Event {$statusTitle} - {$event->title}")
            ->greeting('Hello ' . $event->organizer->name . '!');

        if ($this->status === 'active') {
            $message->line('Great news! Your event has been approved and is now live on TickGet.')
                ->line('')
                ->line('**Event Details:**')
                ->line('Title: ' . $event->title)
                ->line('Date: ' . $event->event_date->format('l, F j, Y'))
                ->line('Time: ' . $event->event_time)
                ->line('Venue: ' . $event->venue)
                ->line('')
                ->line('Your event is now visible to the public and people can start purchasing tickets.')
                ->action('View Your Event', route('events.show', $event->slug))
                ->line('')
                ->line('**Next Steps:**')
                ->line('• Share your event link on social media')
                ->line('• Monitor ticket sales from your dashboard')
                ->line('• Prepare for a successful event!')
                ->line('')
                ->line('If you need to make any changes to your event, please contact our support team.');
        } else {
            $message->line('We need to inform you that your event has been deactivated.')
                ->line('')
                ->line('**Event Details:**')
                ->line('Title: ' . $event->title)
                ->line('Date: ' . $event->event_date->format('l, F j, Y'))
                ->line('Deactivated Date: ' . now()->format('F j, Y \a\t g:i A'));
            
            if ($this->adminNotes) {
                $message->line('Reason: ' . $this->adminNotes);
            }
            
            $message->line('')
                ->line('Your event is no longer visible to the public and ticket sales have been suspended.')
                ->line('If you believe this is an error or need clarification, please contact our support team.')
                ->action('Contact Support', route('dashboard'));
        }

        return $message->salutation('Best regards, The TickGet Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'event_id' => $this->event->id,
            'event_title' => $this->event->title,
            'status' => $this->status,
            'admin_notes' => $this->adminNotes,
        ];
    }
}
