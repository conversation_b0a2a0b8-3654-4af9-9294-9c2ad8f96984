import { useState, useEffect } from 'react';
import { Head, <PERSON> } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  ClockIcon,
  MapPinIcon,
  UserGroupIcon,
  ShareIcon,
  HeartIcon,
  TicketIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  MinusIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import TicketLayout from '../../Layouts/TicketLayout';
import ErrorAlert from '../../Components/Notifications/ErrorAlert';

const EventDetails = ({ event = {} }) => {
  const [selectedTicketType, setSelectedTicketType] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [isFavorited, setIsFavorited] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [buyerInfo, setBuyerInfo] = useState({
    name: '',
    email: '',
    phone: ''
  });
  const [selectedImage, setSelectedImage] = useState(null);
  const [countdown, setCountdown] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: false
  });

  // Use real event data from backend
  const eventData = event;

  // Debug logging (only log once)
  useEffect(() => {
    console.log('Event Data:', {
      event_date: eventData.event_date,
      event_time: eventData.event_time,
      is_active: eventData.is_active,
      status: eventData.status
    });
  }, [eventData.id]); // Only log when event changes

  // Debug logging (can be removed in production)
  // console.log('Event data received:', eventData);
  // console.log('Gallery images:', eventData.gallery_images);
  // console.log('Tickets:', eventData.tickets);

  // Check if event has passed
  const isEventPassed = () => {
    if (!eventData.event_date || !eventData.event_time) return false;

    try {
      // Handle different date formats
      let eventDateTime;
      if (eventData.event_date.includes('T')) {
        // ISO format
        eventDateTime = new Date(eventData.event_date);
      } else {
        // Combine date and time
        eventDateTime = new Date(`${eventData.event_date}T${eventData.event_time}`);
      }

      // console.log('Event DateTime:', eventDateTime);

      return new Date() > eventDateTime;
    } catch (error) {
      console.error('Date parsing error:', error);
      return false;
    }
  };

  // Countdown logic
  useEffect(() => {
    if (!eventData.event_date || !eventData.event_time) {
      console.log('Missing event date or time:', { date: eventData.event_date, time: eventData.event_time });
      return;
    }

    const updateCountdown = () => {
      try {
        // Handle different date formats
        let eventDateTime;
        if (eventData.event_date.includes('T')) {
          // ISO format
          eventDateTime = new Date(eventData.event_date);
        } else {
          // Combine date and time
          eventDateTime = new Date(`${eventData.event_date}T${eventData.event_time}`);
        }

        // console.log('Countdown - Event DateTime:', eventDateTime);

        if (isNaN(eventDateTime.getTime())) {
          console.error('Invalid event date/time:', { date: eventData.event_date, time: eventData.event_time });
          setCountdown({
            days: 0,
            hours: 0,
            minutes: 0,
            seconds: 0,
            isExpired: true
          });
          return;
        }

        const now = new Date();
        const difference = eventDateTime.getTime() - now.getTime();

        // console.log('Time difference:', difference);

        if (difference <= 0) {
          setCountdown({
            days: 0,
            hours: 0,
            minutes: 0,
            seconds: 0,
            isExpired: true
          });
          return;
        }

        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        // console.log('Countdown values:', { days, hours, minutes, seconds });

        setCountdown({
          days: isNaN(days) ? 0 : days,
          hours: isNaN(hours) ? 0 : hours,
          minutes: isNaN(minutes) ? 0 : minutes,
          seconds: isNaN(seconds) ? 0 : seconds,
          isExpired: false
        });
      } catch (error) {
        console.error('Countdown calculation error:', error);
        setCountdown({
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          isExpired: true
        });
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [eventData.event_date, eventData.event_time]);

  // Format date
  const formatDate = (date) => {
    if (!date) return '';
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      return dateObj.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return '';
    }
  };

  // Format time
  const formatTime = (time) => {
    if (!time) return '';
    try {
      if (typeof time === 'string' && time.includes(':')) {
        const timeObj = new Date(`2024-01-01T${time}`);
        if (isNaN(timeObj.getTime())) return time;
        return timeObj.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      }
      return time;
    } catch (error) {
      console.error('Time formatting error:', error);
      return time;
    }
  };

  const handleTicketSelect = (ticketType) => {
    setSelectedTicketType(ticketType);
    setQuantity(1);
  };

  const handleQuantityChange = (change) => {
    const newQuantity = quantity + change;
    const maxAvailable = selectedTicketType ?
      (selectedTicketType.quantity_available || 0) - (selectedTicketType.quantity_sold || 0) : 0;
    const maxPurchase = selectedTicketType?.max_purchase || 10;
    const maxAllowed = Math.min(maxPurchase, maxAvailable);

    if (newQuantity >= (selectedTicketType?.min_purchase || 1) && newQuantity <= maxAllowed) {
      setQuantity(newQuantity);
    }
  };

  const handleShare = (platform) => {
    const url = window.location.href;
    const text = `Check out ${eventData.title}`;
    
    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`);
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`);
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`);
        break;
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`);
        break;
    }
    setShowShareMenu(false);
  };

  const getTicketAvailabilityStatus = (ticket) => {
    const available = (ticket.quantity_available || 0) - (ticket.quantity_sold || 0);
    const total = ticket.quantity_available || ticket.total_quantity || 0;

    if (available <= 0) return { status: 'sold-out', color: 'text-red-600', bg: 'bg-red-100' };

    const percentage = total > 0 ? (available / total) * 100 : 100;
    if (percentage <= 10) return { status: 'limited', color: 'text-orange-600', bg: 'bg-orange-100' };
    return { status: 'available', color: 'text-green-600', bg: 'bg-green-100' };
  };

  const handlePurchase = () => {
    // Prevent multiple submissions
    if (isProcessing) {
      console.log('Purchase already in progress, ignoring click');
      return;
    }

    console.log('Purchase button clicked!');

    // Check if event is still available
    if (!eventData.is_active) {
      setErrorMessage('This event is no longer active. Tickets are not available for purchase.');
      setShowErrorAlert(true);
      return;
    }

    if (countdown.isExpired || isEventPassed()) {
      setErrorMessage('This event has already started. Tickets are no longer available for purchase.');
      setShowErrorAlert(true);
      return;
    }

    if (!selectedTicketType) {
      setErrorMessage('Please select a ticket type.');
      setShowErrorAlert(true);
      return;
    }

    if (quantity < 1) {
      setErrorMessage('Please select at least 1 ticket.');
      setShowErrorAlert(true);
      return;
    }

    if (!buyerInfo.name.trim()) {
      setErrorMessage('Please enter your name.');
      setShowErrorAlert(true);
      return;
    }

    if (!buyerInfo.email.trim()) {
      setErrorMessage('Please enter your email address.');
      setShowErrorAlert(true);
      return;
    }

    console.log('All validations passed, processing purchase...');
    setIsProcessing(true);

    // Simple approach: redirect to a GET route that handles the order creation
    const orderData = {
      ticket_id: selectedTicketType.id,
      quantity: quantity,
      buyer_name: buyerInfo.name,
      buyer_email: buyerInfo.email,
      buyer_phone: buyerInfo.phone || '',
      notes: ''
    };

    // Store order data in sessionStorage and redirect
    sessionStorage.setItem('orderData', JSON.stringify(orderData));

    console.log('Redirecting to order creation page...');
    window.location.href = `/event/${eventData.slug}/create-order`;
  };

  return (
    <TicketLayout>
      <Head title={`${eventData.title} - TickGet`} />
      
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div
          className="relative h-[500px] bg-gradient-to-r from-indigo-600 to-purple-600"
          style={{
            backgroundImage: eventData.banner_image ? `url(/storage/${eventData.banner_image})` : undefined,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center">
            <div className="text-white">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <div className="flex items-center space-x-2 mb-4">
                  {eventData.category && (
                    <span className="bg-black bg-opacity-20 px-3 py-1 rounded-full text-sm font-medium">
                      {eventData.category}
                    </span>
                  )}
                  {eventData.organizer && (
                    <CheckCircleIcon className="h-5 w-5 text-green-400" />
                  )}
                </div>
                <h1 className="text-4xl md:text-6xl font-bold mb-4">{eventData.title}</h1>
                <p className="text-xl md:text-2xl text-gray-200 mb-6 max-w-3xl">
                  {eventData.short_description || eventData.description}
                </p>
                <div className="flex flex-wrap items-center gap-6 text-lg">
                  <div className="flex items-center">
                    <CalendarIcon className="h-6 w-6 mr-2" />
                    {formatDate(eventData.event_date || eventData.date)}
                  </div>
                  <div className="flex items-center">
                    <ClockIcon className="h-6 w-6 mr-2" />
                    {formatTime(eventData.event_time || eventData.time)}
                  </div>
                  <div className="flex items-center">
                    <MapPinIcon className="h-6 w-6 mr-2" />
                    {eventData.venue}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Event Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
              >
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  {/* Available Tickets */}
                  <div className="text-center">
                    <div className="bg-green-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                      <TicketIcon className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="text-2xl font-bold text-gray-900">
                      {eventData.tickets ? eventData.tickets.reduce((total, ticket) => {
                        return total + ((ticket.quantity_available || 0) - (ticket.quantity_sold || 0));
                      }, 0) : 0}
                    </div>
                    <div className="text-sm text-gray-600">Available Tickets</div>
                  </div>

                  {/* Countdown Timer */}
                  {!countdown.isExpired && !isEventPassed() && eventData.event_date && eventData.event_time ? (
                    <>
                      <div className="text-center">
                        <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                          <ClockIcon className="h-6 w-6 text-blue-600" />
                        </div>
                        <div className="text-2xl font-bold text-gray-900">{countdown.days || 0}</div>
                        <div className="text-sm text-gray-600">Days</div>
                      </div>
                      <div className="text-center">
                        <div className="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                          <ClockIcon className="h-6 w-6 text-purple-600" />
                        </div>
                        <div className="text-2xl font-bold text-gray-900">{countdown.hours || 0}</div>
                        <div className="text-sm text-gray-600">Hours</div>
                      </div>
                      <div className="text-center">
                        <div className="bg-orange-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                          <ClockIcon className="h-6 w-6 text-orange-600" />
                        </div>
                        <div className="text-2xl font-bold text-gray-900">{countdown.minutes || 0}</div>
                        <div className="text-sm text-gray-600">Minutes</div>
                      </div>
                    </>
                  ) : (
                    <div className="col-span-3 text-center">
                      <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                        <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
                      </div>
                      <div className="text-2xl font-bold text-red-600">
                        {!eventData.event_date || !eventData.event_time ? 'Event Date TBD' : 'Event Has Started'}
                      </div>
                      <div className="text-sm text-gray-600 mt-2">
                        {!eventData.event_date || !eventData.event_time
                          ? 'Event date and time will be announced soon'
                          : 'This event has already begun or has ended'
                        }
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Event Description */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">About This Event</h2>
                <div className="prose prose-indigo max-w-none">
                  <div dangerouslySetInnerHTML={{ __html: eventData.description || eventData.long_description }} />
                  {eventData.additional_info && (
                    <div className="mt-4">
                      <h3 className="text-lg font-semibold mb-2">Additional Information</h3>
                      <div dangerouslySetInnerHTML={{ __html: eventData.additional_info }} />
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Event Gallery */}
              {eventData.gallery_images && eventData.gallery_images.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.35 }}
                  className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Event Gallery</h2>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {eventData.gallery_images.map((image, index) => {
                      // Handle different image formats
                      let imageSrc = '';

                      if (typeof image === 'string') {
                        // If it's a string, it could be a full URL or a path
                        if (image.startsWith('http://') || image.startsWith('https://')) {
                          imageSrc = image;
                        } else if (image.startsWith('/storage/')) {
                          imageSrc = image;
                        } else if (image.startsWith('storage/')) {
                          imageSrc = `/${image}`;
                        } else {
                          imageSrc = `/storage/${image}`;
                        }
                      } else if (image && typeof image === 'object') {
                        // Handle object format
                        const imageUrl = image.url || image.path || image.src || image.file_path;
                        if (imageUrl) {
                          if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
                            imageSrc = imageUrl;
                          } else if (imageUrl.startsWith('/storage/')) {
                            imageSrc = imageUrl;
                          } else if (imageUrl.startsWith('storage/')) {
                            imageSrc = `/${imageUrl}`;
                          } else {
                            imageSrc = `/storage/${imageUrl}`;
                          }
                        }
                      }

                      if (!imageSrc) {
                        return null;
                      }

                      return (
                        <div
                          key={index}
                          className="relative group cursor-pointer rounded-lg shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden bg-gray-100"
                          onClick={() => setSelectedImage(imageSrc)}
                        >
                          <img
                            src={imageSrc}
                            alt={`${eventData.title} gallery image ${index + 1}`}
                            className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                            onLoad={(e) => {
                              e.target.style.backgroundColor = 'transparent';
                            }}
                            onError={(e) => {
                              e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNzUgMTIwSDIyNVYxODBIMTc1VjEyMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE1MCAyMTBIMjUwVjE4MEgxNTBWMjEwWiIgZmlsbD0iIzlDQTNBRiIvPgo8Y2lyY2xlIGN4PSIxODAiIGN5PSIxNDAiIHI9IjEwIiBmaWxsPSIjOUNBM0FGIi8+Cjx0ZXh0IHg9IjIwMCIgeT0iMjQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNkI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+Cjwvc3ZnPgo=';
                              e.target.alt = 'Image not available';
                            }}
                          />
                        </div>
                      );
                    })}
                  </div>
                </motion.div>
              )}

              {/* Event Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Event Details</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Date & Time</h3>
                    <div className="space-y-2 text-gray-600">
                      <div className="flex items-center">
                        <CalendarIcon className="h-5 w-5 mr-2" />
                        {formatDate(eventData.event_date || eventData.date)}
                        {(eventData.end_date || eventData.end_date) && ` - ${formatDate(eventData.end_date || eventData.end_date)}`}
                      </div>
                      <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 mr-2" />
                        {formatTime(eventData.event_time || eventData.time)}
                        {(eventData.end_time || eventData.end_time) && ` - ${formatTime(eventData.end_time || eventData.end_time)}`}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Location</h3>
                    <div className="space-y-2 text-gray-600">
                      <div className="flex items-start">
                        <MapPinIcon className="h-5 w-5 mr-2 mt-0.5" />
                        <div>
                          <div>{eventData.venue}</div>
                          <div>{eventData.address}</div>
                          <div>{eventData.city}, {eventData.state}, {eventData.country}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Tags */}
                {eventData.category && (
                  <div className="mt-6">
                    <h3 className="font-semibold text-gray-900 mb-2">Category</h3>
                    <div className="flex flex-wrap gap-2">
                      <span className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">
                        {eventData.category}
                      </span>
                      {eventData.event_type && (
                        <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
                          {eventData.event_type}
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </motion.div>

              {/* Organizer Info */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                className="bg-white rounded-lg shadow-lg p-6"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Organized By</h2>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-16 w-16 bg-indigo-100 rounded-lg flex items-center justify-center">
                      <UserGroupIcon className="h-8 w-8 text-indigo-600" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {eventData.organizer?.name || 'Event Organizer'}
                      </h3>
                      <CheckCircleIcon className="h-5 w-5 text-green-500 ml-2" />
                    </div>
                    <p className="text-gray-600 mt-1">
                      {eventData.organizer?.email || 'Professional event organizer'}
                    </p>
                    <div className="flex space-x-4 mt-3">
                      {eventData.website_url && (
                        <a href={eventData.website_url} className="text-indigo-600 hover:text-indigo-800 text-sm">
                          Website
                        </a>
                      )}
                      {eventData.contact_email && (
                        <a href={`mailto:${eventData.contact_email}`} className="text-indigo-600 hover:text-indigo-800 text-sm">
                          Contact
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Terms & Conditions */}
              {eventData.terms_conditions && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Terms & Conditions</h2>
                  <div className="prose prose-indigo max-w-none text-gray-600">
                    <div dangerouslySetInnerHTML={{ __html: eventData.terms_conditions }} />
                  </div>
                </motion.div>
              )}

              {/* Event Tags */}
              {eventData.tags && eventData.tags.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.65 }}
                  className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Tags</h2>
                  <div className="flex flex-wrap gap-2">
                    {eventData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-indigo-100 text-indigo-800 px-3 py-1.5 rounded-full text-sm font-medium"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Social Links */}
              {eventData.social_links && Object.keys(eventData.social_links).length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.7 }}
                  className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Follow Us</h2>
                  <div className="flex flex-wrap gap-3">
                    {eventData.social_links.facebook && (
                      <a
                        href={eventData.social_links.facebook}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        Facebook
                      </a>
                    )}
                    {eventData.social_links.twitter && (
                      <a
                        href={eventData.social_links.twitter}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center px-4 py-2 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        Twitter
                      </a>
                    )}
                    {eventData.social_links.instagram && (
                      <a
                        href={eventData.social_links.instagram}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.608c-.384 0-.735-.157-.99-.411-.254-.255-.411-.606-.411-.99 0-.384.157-.735.411-.99.255-.254.606-.411.99-.411.384 0 .735.157.99.411.254.255.411.606.411.99 0 .384-.157.735-.411.99-.255.254-.606.411-.99.411z"/>
                        </svg>
                        Instagram
                      </a>
                    )}
                    {eventData.social_links.linkedin && (
                      <a
                        href={eventData.social_links.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn
                      </a>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Contact Information */}
              {(eventData.contact_phone || eventData.contact_email) && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.75 }}
                  className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Information</h2>
                  <div className="space-y-3">
                    {eventData.contact_email && (
                      <div className="flex items-center">
                        <svg className="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <a href={`mailto:${eventData.contact_email}`} className="text-indigo-600 hover:text-indigo-800">
                          {eventData.contact_email}
                        </a>
                      </div>
                    )}
                    {eventData.contact_phone && (
                      <div className="flex items-center">
                        <svg className="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <a href={`tel:${eventData.contact_phone}`} className="text-indigo-600 hover:text-indigo-800">
                          {eventData.contact_phone}
                        </a>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Event Capacity */}
              {eventData.max_attendees && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  className="bg-white rounded-xl shadow-lg p-6 border border-gray-100"
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Event Capacity</h2>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <UserGroupIcon className="h-6 w-6 text-gray-400 mr-2" />
                      <span className="text-gray-600">Maximum Attendees</span>
                    </div>
                    <span className="text-2xl font-bold text-indigo-600">{eventData.max_attendees}</span>
                  </div>
                  {eventData.current_attendees !== undefined && (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm text-gray-600 mb-2">
                        <span>Current Registrations</span>
                        <span>{eventData.current_attendees} / {eventData.max_attendees}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min((eventData.current_attendees / eventData.max_attendees) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </div>

            {/* Sidebar - Ticket Selection */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="sticky top-8"
              >
                <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
                  {/* Inactive Event Warning */}
                  {!eventData.is_active && (
                    <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center">
                        <ExclamationTriangleIcon className="h-6 w-6 text-red-600 mr-3" />
                        <div>
                          <h3 className="text-lg font-semibold text-red-800">Event Unavailable</h3>
                          <p className="text-red-700 mt-1">
                            This event is no longer active. Tickets are currently unavailable for purchase.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Event Passed Warning */}
                  {eventData.is_active && (countdown.isExpired || isEventPassed()) && (
                    <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="flex items-center">
                        <ClockIcon className="h-6 w-6 text-orange-600 mr-3" />
                        <div>
                          <h3 className="text-lg font-semibold text-orange-800">Event Has Started</h3>
                          <p className="text-orange-700 mt-1">
                            This event has already begun or has ended. Tickets are no longer available for purchase.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex space-x-2 mb-6">
                    <button
                      onClick={() => setIsFavorited(!isFavorited)}
                      className={`flex-1 flex items-center justify-center py-2 px-4 rounded-md border transition-colors duration-200 ${
                        isFavorited
                          ? 'bg-red-50 border-red-200 text-red-700'
                          : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {isFavorited ? (
                        <HeartSolidIcon className="h-5 w-5 mr-2" />
                      ) : (
                        <HeartIcon className="h-5 w-5 mr-2" />
                      )}
                      {isFavorited ? 'Favorited' : 'Favorite'}
                    </button>
                    
                    <div className="relative">
                      <button
                        onClick={() => setShowShareMenu(!showShareMenu)}
                        className="flex items-center justify-center py-2 px-4 rounded-md border border-gray-200 text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                      >
                        <ShareIcon className="h-5 w-5" />
                      </button>
                      
                      {showShareMenu && (
                        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                          <div className="py-1">
                            <button onClick={() => handleShare('twitter')} className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                              Share on Twitter
                            </button>
                            <button onClick={() => handleShare('facebook')} className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                              Share on Facebook
                            </button>
                            <button onClick={() => handleShare('linkedin')} className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                              Share on LinkedIn
                            </button>
                            <button onClick={() => handleShare('whatsapp')} className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                              Share on WhatsApp
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-gray-900">Select Tickets</h2>
                    {(!selectedTicketType && eventData.tickets && eventData.tickets.length > 0 && eventData.is_active) && (
                      <Link
                        href={`/event/${eventData.slug}`}
                        className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 flex items-center shadow-md hover:shadow-lg"
                      >
                        <TicketIcon className="h-4 w-4 mr-2" />
                        Buy Tickets
                      </Link>
                    )}
                  </div>
                  
                  {/* Ticket Types */}
                  <div className="space-y-4 mb-6">
                    {(!eventData.tickets || eventData.tickets.length === 0) ? (
                      <div className="text-center py-8 bg-gray-50 rounded-lg">
                        <TicketIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No Tickets Available</h3>
                        <p className="text-gray-600">Tickets for this event are not yet available or have been sold out.</p>
                      </div>
                    ) : (
                      eventData.tickets.map((ticket) => {
                      const availability = getTicketAvailabilityStatus(ticket);
                      const isSelected = selectedTicketType?.id === ticket.id;
                      
                      return (
                        <div
                          key={ticket.id}
                          className={`border rounded-lg p-4 transition-all duration-200 ${
                            !eventData.is_active || availability.status === 'sold-out' || countdown.isExpired || isEventPassed()
                              ? 'opacity-50 cursor-not-allowed bg-gray-50'
                              : isSelected
                              ? 'border-indigo-500 bg-indigo-50 cursor-pointer'
                              : 'border-gray-200 hover:border-gray-300 cursor-pointer'
                          }`}
                          onClick={() => eventData.is_active && availability.status !== 'sold-out' && !countdown.isExpired && !isEventPassed() && handleTicketSelect(ticket)}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h3 className="font-semibold text-gray-900">{ticket.name}</h3>
                              <p className="text-sm text-gray-600">{ticket.description || 'Event ticket'}</p>
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold text-gray-900">
                                {ticket.price === 0 ? 'Free' : `₦${Number(ticket.price).toLocaleString()}`}
                              </div>
                            </div>
                          </div>
                          
                          {/* Availability Status */}
                          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            countdown.isExpired || isEventPassed()
                              ? 'bg-red-100 text-red-800'
                              : `${availability.bg} ${availability.color}`
                          }`}>
                            {(countdown.isExpired || isEventPassed()) && (
                              <>
                                <ClockIcon className="h-3 w-3 mr-1" />
                                Event Started
                              </>
                            )}
                            {!(countdown.isExpired || isEventPassed()) && availability.status === 'sold-out' && (
                              <>
                                <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                                Sold Out
                              </>
                            )}
                            {!(countdown.isExpired || isEventPassed()) && availability.status === 'limited' && (
                              <>
                                <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                                Only {(ticket.quantity_available || 0) - (ticket.quantity_sold || 0)} left
                              </>
                            )}
                            {!(countdown.isExpired || isEventPassed()) && availability.status === 'available' && (
                              <>
                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                {(ticket.quantity_available || 0) - (ticket.quantity_sold || 0)} available
                              </>
                            )}
                          </div>

                          {/* Features */}
                          {ticket.benefits && ticket.benefits.length > 0 && (
                            <div className="mt-3">
                              <ul className="text-sm text-gray-600 space-y-1">
                                {ticket.benefits.slice(0, 3).map((feature, index) => (
                                  <li key={index} className="flex items-center">
                                    <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                                    {feature}
                                  </li>
                                ))}
                                {ticket.benefits.length > 3 && (
                                  <li className="text-indigo-600 text-xs">
                                    +{ticket.benefits.length - 3} more features
                                  </li>
                                )}
                              </ul>
                            </div>
                          )}
                        </div>
                      );
                    }))}
                  </div>

                  {/* Quantity Selection */}
                  {selectedTicketType && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      transition={{ duration: 0.3 }}
                      className="mb-6"
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Quantity
                      </label>
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => handleQuantityChange(-1)}
                          disabled={quantity <= (selectedTicketType?.min_purchase || 1)}
                          className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <MinusIcon className="h-4 w-4" />
                        </button>
                        <span className="text-lg font-semibold w-8 text-center">{quantity}</span>
                        <button
                          onClick={() => handleQuantityChange(1)}
                          disabled={quantity >= Math.min(
                            selectedTicketType?.max_purchase || 10,
                            (selectedTicketType?.quantity_available || 0) - (selectedTicketType?.quantity_sold || 0)
                          )}
                          className="w-10 h-10 rounded-md border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <PlusIcon className="h-4 w-4" />
                        </button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Maximum 10 tickets per order
                      </p>
                    </motion.div>
                  )}

                  {/* Buyer Information */}
                  {selectedTicketType && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      transition={{ duration: 0.3 }}
                      className="mb-6"
                    >
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">Buyer Information</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Full Name *
                          </label>
                          <input
                            type="text"
                            value={buyerInfo.name}
                            onChange={(e) => setBuyerInfo({...buyerInfo, name: e.target.value})}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="Enter your full name"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email Address *
                          </label>
                          <input
                            type="email"
                            value={buyerInfo.email}
                            onChange={(e) => setBuyerInfo({...buyerInfo, email: e.target.value})}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="Enter your email address"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Phone Number (Optional)
                          </label>
                          <input
                            type="tel"
                            value={buyerInfo.phone}
                            onChange={(e) => setBuyerInfo({...buyerInfo, phone: e.target.value})}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="Enter your phone number"
                          />
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {/* Total and Purchase Button */}
                  {selectedTicketType && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      transition={{ duration: 0.3 }}
                    >
                      <div className="border-t pt-4 mb-4">
                        <div className="flex justify-between items-center text-lg font-semibold">
                          <span>Total:</span>
                          <span>₦{(selectedTicketType.price * quantity).toLocaleString()}</span>
                        </div>
                      </div>
                      
                      <button
                        onClick={handlePurchase}
                        disabled={isProcessing || !eventData.is_active || countdown.isExpired || isEventPassed()}
                        className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 px-6 rounded-lg font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <TicketIcon className="h-5 w-5 mr-2" />
                        {isProcessing ? 'Processing...' :
                         !eventData.is_active ? 'Event Unavailable' :
                         (countdown.isExpired || isEventPassed()) ? 'Event Has Started' :
                         'Purchase Tickets'}
                      </button>

                      {/* Purchase disabled message */}
                      {(!eventData.is_active || countdown.isExpired || isEventPassed()) && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <div className="flex items-center text-sm text-red-700">
                            <ExclamationTriangleIcon className="h-4 w-4 mr-2 flex-shrink-0" />
                            {!eventData.is_active ? 'This event is no longer active.' :
                             (countdown.isExpired || isEventPassed()) ? 'This event has already started. Tickets are no longer available.' :
                             'Tickets are currently unavailable.'}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}

                  {/* Event Info */}
                  {(eventData.terms_conditions || eventData.contact_email || eventData.contact_phone) && (
                    <div className="mt-6 pt-6 border-t">
                      <h3 className="font-semibold text-gray-900 mb-3">Event Information</h3>
                      <div className="space-y-2 text-sm text-gray-600">
                        {eventData.contact_email && (
                          <div className="flex items-start">
                            <InformationCircleIcon className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
                            <span>Contact: {eventData.contact_email}</span>
                          </div>
                        )}
                        {eventData.contact_phone && (
                          <div className="flex items-start">
                            <InformationCircleIcon className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
                            <span>Phone: {eventData.contact_phone}</span>
                          </div>
                        )}
                        {eventData.terms_conditions && (
                          <div className="flex items-start">
                            <InformationCircleIcon className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
                            <span>Please review terms and conditions before purchasing</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={selectedImage}
              alt="Gallery image"
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-all duration-200"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      )}

      {/* Error Alert */}
      <ErrorAlert
        show={showErrorAlert}
        onClose={() => setShowErrorAlert(false)}
        title="Purchase Error"
        message={errorMessage}
      />
    </TicketLayout>
  );
};

export default EventDetails;
