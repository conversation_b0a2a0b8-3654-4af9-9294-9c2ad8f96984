import React, { useState, useEffect } from 'react';
import { Head, router } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  CreditCardIcon,
  ShieldCheckIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import TicketLayout from '../../Layouts/TicketLayout';
import ErrorAlert from '../../Components/Notifications/ErrorAlert';

const Payment = ({ order, paystack }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutes in seconds
  const [paystackLoaded, setPaystackLoaded] = useState(false);

  // Load Paystack script
  useEffect(() => {
    const loadPaystackScript = () => {
      // Check if script is already loaded
      if (window.PaystackPop) {
        setPaystackLoaded(true);
        return;
      }

      // Check if script tag already exists
      const existingScript = document.querySelector('script[src="https://js.paystack.co/v1/inline.js"]');
      if (existingScript) {
        existingScript.onload = () => setPaystackLoaded(true);
        return;
      }

      // Create and load script
      const script = document.createElement('script');
      script.src = 'https://js.paystack.co/v1/inline.js';
      script.async = true;
      script.onload = () => setPaystackLoaded(true);
      script.onerror = () => {
        setError('Failed to load payment system. Please refresh the page.');
        setShowErrorAlert(true);
      };
      document.head.appendChild(script);
    };

    loadPaystackScript();
  }, []);

  // Countdown timer
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timer);
          // Redirect to event page when time expires
          router.visit(`/event/${order.event.slug}`, {
            replace: true,
            onSuccess: () => {
              setError('Payment session expired. Please try again.');
              setShowErrorAlert(true);
            }
          });
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [order.event.slug]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatPrice = (price) => {
    return `₦${Number(price).toLocaleString()}`;
  };

  const handlePayment = () => {
    if (!window.PaystackPop || !paystackLoaded) {
      setError('Payment system not loaded. Please refresh the page.');
      setShowErrorAlert(true);
      return;
    }

    setIsProcessing(true);
    setError('');

    const handler = window.PaystackPop.setup({
      key: paystack.public_key,
      email: order.buyer_email,
      amount: order.total_amount * 100, // Convert to kobo
      ref: paystack.reference,
      currency: 'NGN',
      metadata: {
        order_id: order.id,
        order_number: order.order_number,
        event_title: order.event.title,
        buyer_name: order.buyer_name,
      },
      callback: function(response) {
        setIsProcessing(false);
        // Redirect to success page
        router.post(`/orders/${order.order_number}/payment/success`, {
          reference: response.reference,
          payment_status: 'success',
        });
      },
      onClose: function() {
        setIsProcessing(false);
        setError('Payment was cancelled. You can try again.');
        setShowErrorAlert(true);
      }
    });

    handler.openIframe();
  };

  const handleCardPayment = () => {
    // Redirect to Paystack hosted page
    window.location.href = paystack.authorization_url;
  };

  return (
    <TicketLayout>
      <Head title={`Payment - ${order.event.title}`} />
      
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Complete Your Payment</h1>
              <p className="text-gray-600">
                Secure payment for {order.event.title}
              </p>
              
              {/* Timer */}
              <div className="mt-4 inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-full">
                <ClockIcon className="h-5 w-5 mr-2" />
                <span className="font-medium">
                  Complete payment within {formatTime(timeLeft)}
                </span>
              </div>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Order Summary */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              <h2 className="text-xl font-bold text-gray-900 mb-6">Order Summary</h2>
              
              {/* Event Info */}
              <div className="border-b pb-4 mb-4">
                <h3 className="font-semibold text-gray-900">{order.event.title}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {new Date(order.event.event_date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
                <p className="text-sm text-gray-600">{order.event.venue}</p>
              </div>

              {/* Order Details */}
              <div className="space-y-3 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Order Number:</span>
                  <span className="font-medium">{order.order_number}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Buyer:</span>
                  <span className="font-medium">{order.buyer_name}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Email:</span>
                  <span className="font-medium">{order.buyer_email}</span>
                </div>
              </div>

              {/* Tickets */}
              <div className="border-t pt-4 mb-4">
                <h4 className="font-medium text-gray-900 mb-3">Tickets</h4>
                <div className="space-y-2">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex justify-between text-sm">
                      <div>
                        <span className="text-gray-900">{item.ticket.name}</span>
                        <span className="text-gray-500 ml-2">× {item.quantity}</span>
                      </div>
                      <span className="font-medium">{formatPrice(item.total_price)}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pricing */}
              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal:</span>
                  <span>{formatPrice(order.subtotal)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Platform Fee (10%):</span>
                  <span>{formatPrice(order.platform_fee)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Total:</span>
                  <span>{formatPrice(order.total_amount)}</span>
                </div>
              </div>
            </motion.div>

            {/* Payment Methods */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="bg-white rounded-lg shadow-lg p-6"
            >
              <h2 className="text-xl font-bold text-gray-900 mb-6">Payment Method</h2>

              {error && (
                <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-2" />
                    <span className="text-sm text-red-700">{error}</span>
                  </div>
                </div>
              )}

              {/* No Refund Notice */}
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <h3 className="text-sm font-semibold text-red-800 mb-1">No Refund Policy</h3>
                    <p className="text-sm text-red-700">
                      Please note that all ticket purchases are final and non-refundable.
                      By proceeding with this payment, you acknowledge and agree to this policy.
                    </p>
                  </div>
                </div>
              </div>

              {/* Payment Options */}
              <div className="space-y-4 mb-6">
                <button
                  onClick={handlePayment}
                  disabled={isProcessing || !paystackLoaded}
                  className="w-full flex items-center justify-center px-6 py-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <CreditCardIcon className="h-6 w-6 text-gray-600 mr-3" />
                  <div className="text-left">
                    <div className="font-medium text-gray-900">
                      {!paystackLoaded ? 'Loading Payment System...' : isProcessing ? 'Processing...' : 'Pay with Card (Popup)'}
                    </div>
                    <div className="text-sm text-gray-600">Visa, Mastercard, Verve</div>
                  </div>
                </button>

                <button
                  onClick={handleCardPayment}
                  disabled={isProcessing}
                  className="w-full flex items-center justify-center px-6 py-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ShieldCheckIcon className="h-6 w-6 text-gray-600 mr-3" />
                  <div className="text-left">
                    <div className="font-medium text-gray-900">Pay with Card (Secure Page)</div>
                    <div className="text-sm text-gray-600">Bank Transfer, USSD, QR Code</div>
                  </div>
                </button>
              </div>

              {/* Security Info */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex">
                  <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2 mt-0.5" />
                  <div className="text-sm">
                    <div className="font-medium text-green-800">Secure Payment</div>
                    <div className="text-green-700 mt-1">
                      Your payment is secured by Paystack with 256-bit SSL encryption.
                      We never store your card details.
                    </div>
                  </div>
                </div>
              </div>

              {/* Processing State */}
              {isProcessing && (
                <div className="mt-4 text-center">
                  <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    Processing payment...
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Error Alert */}
      <ErrorAlert
        show={showErrorAlert}
        onClose={() => setShowErrorAlert(false)}
        title="Payment Error"
        message={error}
      />
    </TicketLayout>
  );
};

export default Payment;
