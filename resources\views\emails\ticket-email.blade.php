<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Ticket - {{ $order->event->title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-top: 20px solid #764ba2;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }
        
        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .event-banner {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: black;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            background-image: url('{{ $order->event->banner_image ?? "" }}');
            background-size: cover;
            background-position: center;
            position: relative;
        }
        
        .event-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(102, 126, 234, 0.8);
            border-radius: 12px;
        }
        
        .event-banner-content {
            position: relative;
            z-index: 1;
        }
        
        .ticket-info {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .info-section {
            margin-bottom: 25px;
        }
        
        .info-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .info-title::before {
            content: '🎫';
            margin-right: 10px;
            font-size: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .info-label {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-size: 16px;
            color: #2d3748;
            font-weight: 600;
        }
        
        .order-details {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .order-title {
            font-size: 18px;
            font-weight: 700;
            color: #22543d;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .order-title::before {
            content: '📋';
            margin-right: 10px;
            font-size: 20px;
        }
        
        .ticket-list {
            list-style: none;
            padding: 0;
        }
        
        .ticket-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #48bb78;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .ticket-name {
            font-weight: 600;
            color: #2d3748;
        }
        
        .ticket-price {
            font-weight: 700;
            color: #22543d;
        }
        
        .qr-section {
            text-align: center;
            background: white;
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .qr-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .qr-code {
            width: 150px;
            height: 150px;
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #718096;
        }
        
        .important-info {
            background-color: #fef5e7;
            border-left: 4px solid #f6ad55;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .important-title {
            font-weight: 600;
            color: #c05621;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            font-size: 16px;
        }
        
        .important-title::before {
            content: '⚠️';
            margin-right: 8px;
            font-size: 18px;
        }
        
        .important-list {
            list-style: none;
            padding: 0;
        }
        
        .important-list li {
            color: #744210;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .important-list li::before {
            content: '•';
            color: #f6ad55;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            text-align: center;
            margin: 20px auto;
            display: block;
            max-width: 250px;
            transition: transform 0.2s;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .footer {
            background-color: #f7fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            font-size: 14px;
            color: #718096;
            margin-bottom: 15px;
        }
        
        .footer-links {
            font-size: 12px;
            color: #a0aec0;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 30px 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .event-banner {
                height: 150px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="images/Tickgetlogo.png" alt="TickGet Logo">
            </div>
            <h1>TickGet</h1>
            <p>Your Trusted Event Ticketing Platform</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                🎉 Your Ticket is Ready!
            </div>
            
            <!-- Event Banner -->
            <div class="event-banner">
                <div class="event-banner-content">
                    {{ $order->event->title }}
                </div>
            </div>

            <!-- Event Information -->
            <div class="ticket-info">
                <div class="info-section">
                    <div class="info-title">Event Details</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Date</div>
                            <div class="info-value">{{ $order->event->event_date->format('l, F j, Y') }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Time</div>
                            <div class="info-value">{{ $order->event->event_time }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Venue</div>
                            <div class="info-value">{{ $order->event->venue }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Location</div>
                            <div class="info-value">{{ $order->event->city }}, {{ $order->event->state }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Details -->
            <div class="order-details">
                <div class="order-title">Order Information</div>
                <div style="margin-bottom: 20px;">
                    <div class="info-label">Order Number</div>
                    <div class="info-value" style="font-size: 18px; color: #22543d;">{{ $order->order_number }}</div>
                </div>
                <div style="margin-bottom: 20px;">
                    <div class="info-label">Total Amount</div>
                    <div class="info-value" style="font-size: 18px; color: #22543d;">₦{{ number_format($order->total_amount, 2) }}</div>
                </div>
                <div style="margin-bottom: 20px;">
                    <div class="info-label">Your Tickets</div>
                    <ul class="ticket-list">
                        @foreach($order->items as $item)
                        <li class="ticket-item">
                            <span class="ticket-name">{{ $item->ticket->name }} × {{ $item->quantity }}</span>
                            <span class="ticket-price">₦{{ number_format($item->total_price, 2) }}</span>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="qr-section">
                <div class="qr-title">Your Digital Ticket</div>
                <div class="qr-code">
                    QR Code<br>
                    <small>{{ $order->order_number }}</small>
                </div>
                <p style="color: #718096; font-size: 14px;">
                    Present this email or scan the QR code at the entrance
                </p>
            </div>

            <!-- Important Information -->
            <div class="important-info">
                <div class="important-title">Important Information</div>
                <ul class="important-list">
                    <li>Please arrive at the venue at least 30 minutes before the event starts</li>
                    <li>Present this email or the QR code at the entrance for verification</li>
                    <li>Keep this email safe as it serves as your ticket</li>
                    <li>Contact the event organizer for any event-specific questions</li>
                    <li>No refunds or exchanges unless the event is cancelled</li>
                </ul>
            </div>

            <!-- Action Button -->
            <a href="{{ route('orders.show', $order->order_number) }}" class="action-button">
                View Full Ticket Details
            </a>

            <div style="text-align: center; margin-top: 30px; color: #718096;">
                <p>Thank you for choosing TickGet! We hope you enjoy the event.</p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">
                This email was sent from TickGet. If you have any questions, please contact our support team.
            </div>
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Contact Support</a>
            </div>
        </div>
    </div>
</body>
</html>
