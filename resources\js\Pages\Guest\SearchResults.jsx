import React, { useState } from 'react';
import { Head, <PERSON>, router } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  CalendarIcon,
  MapPinIcon,
  TagIcon,
  ArrowLeftIcon,
} from '@heroicons/react/24/outline';
import GuestLayout from '../../Layouts/GuestLayout';

const SearchResults = ({ events, query }) => {
  const [searchQuery, setSearchQuery] = useState(query);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.get('/events/search', { q: searchQuery });
    }
  };

  const formatPrice = (price) => {
    return price === 0 ? 'Free' : `₦${price.toLocaleString()}`;
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTime = (time) => {
    return new Date(`2000-01-01 ${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  return (
    <GuestLayout>
      <Head title={`Search Results for "${query}"`} />
      
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <Link
              href="/events"
              className="inline-flex items-center text-indigo-200 hover:text-white mb-4"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to all events
            </Link>
            <h1 className="text-4xl font-bold mb-4">
              Search Results for "{query}"
            </h1>
            <p className="text-xl text-indigo-100 mb-8">
              {events.total} {events.total === 1 ? 'event' : 'events'} found
            </p>
            
            {/* Search Bar */}
            <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search events, venues, or categories..."
                  className="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-300"
                />
                <MagnifyingGlassIcon className="absolute left-4 top-3.5 h-5 w-5 text-gray-400" />
                <button
                  type="submit"
                  className="absolute right-2 top-2 px-4 py-1.5 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                >
                  Search
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {events.data.length === 0 ? (
          <div className="text-center py-12">
            <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">No events found</h3>
            <p className="mt-1 text-sm text-gray-500">
              We couldn't find any events matching "{query}". Try searching with different keywords.
            </p>
            <div className="mt-6">
              <Link
                href="/events"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Browse all events
              </Link>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {events.data.map((event, index) => (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-lg shadow-sm border overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <Link href={`/event/${event.slug}`}>
                    <div className="aspect-w-16 aspect-h-9">
                      <img
                        src={event.banner_image ? `/storage/${event.banner_image}` : '/images/event-placeholder.jpg'}
                        alt={event.title}
                        className="w-full h-48 object-cover"
                      />
                    </div>
                    <div className="p-6">
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        {formatDate(event.event_date)} at {formatTime(event.event_time)}
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                        {event.title}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500 mb-3">
                        <MapPinIcon className="h-4 w-4 mr-1" />
                        {event.venue}, {event.city}
                      </div>
                      {event.category && (
                        <div className="flex items-center text-sm text-gray-500 mb-3">
                          <TagIcon className="h-4 w-4 mr-1" />
                          {event.category}
                        </div>
                      )}
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-indigo-600">
                          {formatPrice(event.min_price)}
                        </span>
                        {event.is_sold_out ? (
                          <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                            Sold Out
                          </span>
                        ) : (
                          <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                            Available
                          </span>
                        )}
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>

            {/* Pagination */}
            {events.links && (
              <div className="mt-8 flex justify-center">
                <nav className="flex items-center space-x-2">
                  {events.links.map((link, index) => (
                    <Link
                      key={index}
                      href={link.url || '#'}
                      className={`px-3 py-2 text-sm rounded-md ${
                        link.active
                          ? 'bg-indigo-600 text-white'
                          : link.url
                          ? 'text-gray-700 hover:bg-gray-100'
                          : 'text-gray-400 cursor-not-allowed'
                      }`}
                      dangerouslySetInnerHTML={{ __html: link.label }}
                    />
                  ))}
                </nav>
              </div>
            )}

            {/* Suggestions */}
            <div className="mt-12 bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Didn't find what you're looking for?
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Try these tips:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Check your spelling</li>
                    <li>• Use more general keywords</li>
                    <li>• Try searching by location or category</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Popular searches:</h4>
                  <div className="flex flex-wrap gap-2">
                    <Link
                      href="/events/search?q=conference"
                      className="px-3 py-1 text-sm bg-white border rounded-full hover:bg-gray-50"
                    >
                      Conference
                    </Link>
                    <Link
                      href="/events/search?q=music"
                      className="px-3 py-1 text-sm bg-white border rounded-full hover:bg-gray-50"
                    >
                      Music
                    </Link>
                    <Link
                      href="/events/search?q=workshop"
                      className="px-3 py-1 text-sm bg-white border rounded-full hover:bg-gray-50"
                    >
                      Workshop
                    </Link>
                    <Link
                      href="/events/search?q=networking"
                      className="px-3 py-1 text-sm bg-white border rounded-full hover:bg-gray-50"
                    >
                      Networking
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </GuestLayout>
  );
};

export default SearchResults;
