import React from 'react';
import { motion } from 'framer-motion';
import Header from '../Components/Header';
import Footer from '../Components/Footer';

const GuestLayout = ({ children }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <motion.main
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="flex-1"
      >
        {children}
      </motion.main>
      <Footer />
    </div>
  );
};

export default GuestLayout;
