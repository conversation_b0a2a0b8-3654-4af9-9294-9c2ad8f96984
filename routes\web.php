<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\WithdrawalController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\ForgotPasswordController;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;




// Guest Routes (redirect if authenticated)
Route::middleware('guest')->group(function () {
    Route::get('/', [EventController::class, 'publicHome'])->name('home');
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
});

// Public routes (accessible to all)
Route::get('/events', [EventController::class, 'publicIndex'])->name('events.public');
Route::get('/events/search', [EventController::class, 'publicSearch'])->name('events.search');

// Logout (accessible to authenticated users)
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Forgot Password Routes (guest only)
Route::middleware('guest')->group(function () {
    Route::get('/forgot-password', [ForgotPasswordController::class, 'showForgotPasswordForm'])->name('password.request');
    Route::post('/forgot-password', [ForgotPasswordController::class, 'sendResetEmail'])->name('password.email');
    Route::get('/reset-password', [ForgotPasswordController::class, 'showResetPasswordForm'])->name('password.reset');
    Route::post('/verify-reset-token', [ForgotPasswordController::class, 'verifyToken'])->name('password.verify');
    Route::post('/reset-password', [ForgotPasswordController::class, 'resetPassword'])->name('password.update');
});

// Test email templates (remove in production)
Route::get('/test-email', function () {
    return view('emails.password-reset', [
        'token' => 'ABC1234',
        'user' => (object) ['name' => 'Test User']
    ]);
});

Route::get('/test-ticket-email', function () {
    $order = App\Models\Order::with(['event', 'items.ticket'])->first();
    if (!$order) {
        return response()->json(['error' => 'No orders found']);
    }
    return view('emails.ticket-email', ['order' => $order]);
});

Route::get('/test-order-confirmation', function () {
    $order = App\Models\Order::with(['event', 'items.ticket'])->first();
    if (!$order) {
        return response()->json(['error' => 'No orders found']);
    }
    return view('emails.order-confirmation', ['order' => $order]);
});

// Test route to check users (remove in production)
Route::get('/test-users', function () {
    $users = App\Models\User::select('id', 'name', 'email', 'role')->get();
    return response()->json($users);
});

// Test email sending (remove in production)
Route::get('/test-email-send/{email}', function ($email) {
    try {
        $user = App\Models\User::where('email', $email)->first();
        if (!$user) {
            return response()->json(['error' => 'User not found']);
        }

        $token = 'TEST123';
        $user->notify(new App\Notifications\PasswordResetNotification($token));

        return response()->json([
            'success' => true,
            'message' => 'Test email sent successfully',
            'email' => $email,
            'token' => $token
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Simple mail test
Route::get('/test-simple-mail/{email}', function ($email) {
    try {
        Mail::raw('This is a test email from TickGet', function ($message) use ($email) {
            $message->to($email)
                    ->subject('Test Email from TickGet');
        });

        return response()->json(['success' => true, 'message' => 'Simple email sent']);
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()]);
    }
});

// Admin Authentication Routes (guest only)
Route::middleware('guest')->group(function () {
    Route::get('/admin/login', [AuthController::class, 'showAdminLogin'])->name('admin.login');
    Route::post('/admin/login', [AuthController::class, 'adminLogin']);
});

Route::post('/admin/logout', [AuthController::class, 'adminLogout'])->name('admin.logout');

// User Routes (Protected)
Route::middleware(['auth', 'active'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [EventController::class, 'dashboard'])->name('dashboard');

    // Profile Management
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::put('/profile', [ProfileController::class, 'updateProfile'])->name('profile.update');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password.update');
    Route::put('/profile/bank-details', [ProfileController::class, 'updateBankDetails'])->name('profile.bank.update');
    Route::put('/profile/notifications', [ProfileController::class, 'updateNotifications'])->name('profile.notifications.update');
    Route::post('/profile/avatar', [ProfileController::class, 'uploadAvatar'])->name('profile.avatar.upload');
    Route::delete('/profile/avatar', [ProfileController::class, 'removeAvatar'])->name('profile.avatar.remove');
    Route::delete('/profile', [ProfileController::class, 'deleteAccount'])->name('profile.delete');

    // Events Management
    Route::resource('events', EventController::class);
    Route::get('events/{event}/tickets', [TicketController::class, 'index'])->name('events.tickets');
    Route::post('events/{event}/tickets', [TicketController::class, 'store'])->name('tickets.store');
    Route::put('events/{event}/tickets/{ticket}', [TicketController::class, 'update'])->name('tickets.update');
    Route::delete('events/{event}/tickets/{ticket}', [TicketController::class, 'destroy'])->name('tickets.destroy');
    Route::post('events/{event}/tickets/order', [TicketController::class, 'updateOrder'])->name('tickets.order');

    // Event management routes
    Route::get('events/{event:slug}/manage', [EventController::class, 'manage'])->name('events.manage');
    Route::post('events/{event:slug}/attendees/{order}/check-in', [EventController::class, 'checkInAttendee'])->name('events.check-in');
    Route::post('events/{event:slug}/verify-ticket', [EventController::class, 'verifyTicket'])->name('events.verify-ticket');

    // Withdrawals
    Route::get('/withdrawals', [WithdrawalController::class, 'index'])->name('withdrawals');
    Route::post('/withdrawals', [WithdrawalController::class, 'store'])->name('withdrawals.store');
    Route::get('/withdrawals/{withdrawal}', [WithdrawalController::class, 'show'])->name('withdrawals.show');
    Route::post('/withdrawals/{withdrawal}/cancel', [WithdrawalController::class, 'cancel'])->name('withdrawals.cancel');

    // Analytics
    Route::get('/analytics', [EventController::class, 'analytics'])->name('analytics');
});

// Ticket Routes (Public)
Route::get('/event/{event:slug}', [OrderController::class, 'showEventTickets'])->name('event.tickets');
Route::get('/event/{event:slug}/create-order', [OrderController::class, 'createOrderFromSession'])->name('orders.create');
Route::post('/event/{event:slug}/order', [OrderController::class, 'store'])->name('orders.store');
Route::get('/orders/{order:order_number}/payment', [OrderController::class, 'showPayment'])->name('orders.payment');
Route::get('/orders/{order:order_number}', [OrderController::class, 'show'])->name('orders.show');
Route::match(['GET', 'POST'], '/orders/{order:order_number}/payment/success', [OrderController::class, 'paymentSuccess'])->name('orders.payment.success');
Route::match(['GET', 'POST'], '/orders/{order:order_number}/payment/failed', [OrderController::class, 'paymentFailed'])->name('orders.payment.failed');
Route::get('/orders/{order:order_number}/download', [OrderController::class, 'downloadTicket'])->name('orders.download');
Route::post('/orders/{order:order_number}/send-email', [OrderController::class, 'sendTicketEmail'])->name('orders.send-email');
Route::post('/tickets/verify', [OrderController::class, 'verifyTicket'])->name('tickets.verify');

// Paystack webhook (no middleware needed)
Route::post('/webhook/paystack', [OrderController::class, 'paystackWebhook'])->name('webhook.paystack');

// Test route to check events
Route::get('/test-events', function () {
    $events = \App\Models\Event::with('tickets')->latest()->take(5)->get(['id', 'title', 'slug', 'is_active']);
    return response()->json([
        'events' => $events,
        'total_events' => \App\Models\Event::count(),
        'tickets_count' => \App\Models\Ticket::count()
    ]);
});

// Test route to check specific event tickets
Route::get('/test-event-tickets/{slug}', function ($slug) {
    $event = \App\Models\Event::where('slug', $slug)
        ->with(['organizer', 'tickets' => function ($query) {
            $query->where('is_active', true)->orderBy('sort_order');
        }])
        ->first();

    if (!$event) {
        return response()->json(['error' => 'Event not found'], 404);
    }

    return response()->json([
        'event' => $event->toArray(),
        'tickets_count' => $event->tickets->count(),
        'active_tickets_count' => $event->tickets->where('is_active', true)->count(),
        'tickets' => $event->tickets->toArray()
    ]);
});

// Test route to debug event creation
Route::get('/debug-event-creation', function () {
    $user = \Illuminate\Support\Facades\Auth::user();

    if (!$user) {
        return response()->json(['error' => 'Please login first'], 401);
    }

    // Check recent events and their tickets
    $recentEvents = \App\Models\Event::with('tickets')
        ->where('organizer_id', $user->id)
        ->latest()
        ->take(3)
        ->get();

    return response()->json([
        'user_id' => $user->id,
        'recent_events' => $recentEvents->map(function($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'slug' => $event->slug,
                'tickets_count' => $event->tickets->count(),
                'tickets' => $event->tickets->toArray(),
                'edit_url' => "/events/{$event->id}/edit",
                'delete_url' => "/events/{$event->id}",
            ];
        }),
        'total_events' => \App\Models\Event::where('organizer_id', $user->id)->count(),
        'total_tickets' => \App\Models\Ticket::whereHas('event', function($q) use ($user) {
            $q->where('organizer_id', $user->id);
        })->count(),
    ]);
})->middleware('auth');

// Test route to check edit functionality
Route::get('/test-edit/{id}', function ($id) {
    $user = \Illuminate\Support\Facades\Auth::user();

    if (!$user) {
        return response()->json(['error' => 'Please login first'], 401);
    }

    $event = \App\Models\Event::find($id);

    if (!$event) {
        return response()->json(['error' => 'Event not found'], 404);
    }

    if ($event->organizer_id !== $user->id) {
        return response()->json(['error' => 'Unauthorized'], 403);
    }

    return response()->json([
        'event_id' => $event->id,
        'event_title' => $event->title,
        'organizer_id' => $event->organizer_id,
        'current_user_id' => $user->id,
        'edit_url' => "/events/{$event->id}/edit",
        'can_edit' => $event->organizer_id === $user->id,
        'route_exists' => \Illuminate\Support\Facades\Route::has('events.edit'),
    ]);
})->middleware('auth');

// Test route to create a sample event with tickets
Route::get('/create-test-event', function () {
    $user = \Illuminate\Support\Facades\Auth::user();

    if (!$user) {
        return response()->json(['error' => 'Please login first'], 401);
    }

    try {
        // Create a test event
        $event = \App\Models\Event::create([
            'organizer_id' => $user->id,
            'title' => 'Test Event with Tickets - ' . now()->format('Y-m-d H:i:s'),
            'slug' => 'test-event-' . time(),
            'description' => 'This is a test event created to test ticket functionality.',
            'short_description' => 'Test event for ticket functionality',
            'event_date' => now()->addDays(30)->format('Y-m-d'),
            'event_time' => '18:00',
            'venue' => 'Test Venue',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'country' => 'Nigeria',
            'category' => 'Technology',
            'contact_email' => $user->email,
            'is_active' => true,
        ]);

        // Create test tickets
        $tickets = [
            [
                'name' => 'Early Bird',
                'description' => 'Early bird special pricing',
                'price' => 5000,
                'quantity_available' => 50,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Regular',
                'description' => 'Regular admission',
                'price' => 8000,
                'quantity_available' => 100,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'VIP',
                'description' => 'VIP access with premium benefits',
                'price' => 15000,
                'quantity_available' => 20,
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($tickets as $ticketData) {
            $event->tickets()->create($ticketData);
        }

        return response()->json([
            'success' => true,
            'message' => 'Test event created successfully!',
            'event' => $event->load('tickets'),
            'event_url' => "/event-details/{$event->slug}",
            'edit_url' => "/events/{$event->id}/edit"
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Failed to create test event: ' . $e->getMessage()
        ], 500);
    }
})->middleware('auth');

// Test route to debug edit issue
Route::get('/test-edit/{id}', function ($id) {
    $event = \App\Models\Event::find($id);
    $user = Auth::user();

    return response()->json([
        'event_exists' => $event ? true : false,
        'event' => $event,
        'user' => $user,
        'user_id' => $user ? $user->id : null,
        'organizer_id' => $event ? $event->organizer_id : null,
        'can_edit' => $event && $user && $event->organizer_id === $user->id
    ]);
})->middleware('auth');

// Direct test route for edit functionality
Route::get('/debug-edit/{id}', function ($id) {
    try {
        $event = \App\Models\Event::findOrFail($id);
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'Not authenticated'], 401);
        }

        if ($event->organizer_id !== $user->id) {
            return response()->json(['error' => 'Not authorized to edit this event'], 403);
        }

        return response()->json([
            'success' => true,
            'message' => 'Edit access granted',
            'event' => $event,
            'redirect_url' => "/events/{$id}/edit"
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Event not found or error occurred',
            'message' => $e->getMessage()
        ], 404);
    }
})->middleware('auth');

// Alternative edit route for testing
Route::get('/edit-event/{id}', function ($id) {
    $event = \App\Models\Event::findOrFail($id);
    $user = Auth::user();

    if ($event->organizer_id !== $user->id) {
        abort(403, 'Unauthorized access to this event.');
    }

    $event->load('tickets');

    return Inertia::render('User/EditEvent', [
        'event' => $event,
        'user' => $user
    ]);
})->middleware('auth');


// Test route to verify fixes
Route::get('/test-fixes', function () {
    $gdAvailable = extension_loaded('gd');

    return response()->json([
        'success' => true,
        'message' => 'All fixes have been applied successfully!',
        'system_info' => [
            'php_version' => PHP_VERSION,
            'gd_extension' => $gdAvailable ? 'Available' : 'Not Available',
            'file_upload_service' => $gdAvailable ? 'FileUploadService (with image processing)' : 'SimpleFileUploadService (basic upload)',
        ],
        'fixes' => [
            'Profile page error fixed',
            'Event creation form updated with modern design',
            'Success modal with shareable links added',
            'Image preview functionality added',
            'Tags input with comma support added',
            'Extended event categories added',
            'Save as draft functionality added',
            'Dashboard query ambiguity fixed',
            'GD extension fallback implemented',
            'Enhanced form styling and UX',
            'Form submission error fixed'
        ]
    ]);
});

// Test route for event creation
Route::get('/test-event-creation', function () {
    $user = \Illuminate\Support\Facades\Auth::user();
    return response()->json([
        'message' => 'Event creation route is accessible',
        'post_route' => route('events.store'),
        'user_authenticated' => $user !== null,
        'user_role' => $user?->role ?? 'guest',
        'csrf_token' => csrf_token()
    ]);
});

// Test route for form submission
Route::post('/test-form-submit', function (\Illuminate\Http\Request $request) {
    return response()->json([
        'message' => 'Form submission test successful',
        'received_data' => $request->all(),
        'files' => $request->allFiles(),
        'content_type' => $request->header('Content-Type')
    ]);
})->middleware('auth');

// Test route to check latest event
Route::get('/test-latest-event', function () {
    $latestEvent = \App\Models\Event::latest()->first();
    return response()->json([
        'latest_event' => $latestEvent ? $latestEvent->toArray() : null,
        'event_count' => \App\Models\Event::count(),
        'active_events' => \App\Models\Event::where('is_active', true)->count(),
    ]);
});

// Admin Routes (Protected)
Route::middleware(['admin'])->prefix('admin')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('admin.dashboard');
    Route::get('/users', [AdminController::class, 'users'])->name('admin.users');
    Route::get('/events', [AdminController::class, 'events'])->name('admin.events');
    Route::get('/withdrawals', [AdminController::class, 'withdrawals'])->name('admin.withdrawals');
    Route::get('/analytics', [AdminController::class, 'analytics'])->name('admin.analytics');
    Route::get('/settings', [AdminController::class, 'settings'])->name('admin.settings');

    // User management
    Route::post('/users/{user}/toggle-status', [AdminController::class, 'toggleUserStatus'])->name('admin.users.toggle-status');

    // Event management
    Route::post('/events/{event}/toggle-status', [AdminController::class, 'toggleEventStatus'])->name('admin.events.toggle-status');

    // Withdrawal management
    Route::post('/withdrawals/{withdrawal}/approve', [AdminController::class, 'approveWithdrawal'])->name('admin.withdrawals.approve');
    Route::post('/withdrawals/{withdrawal}/reject', [AdminController::class, 'rejectWithdrawal'])->name('admin.withdrawals.reject');
    Route::post('/withdrawals/{withdrawal}/complete', [AdminController::class, 'completeWithdrawal'])->name('admin.withdrawals.complete');

    // Admin management
    Route::post('/admins', [AdminController::class, 'createAdmin'])->name('admin.admins.create');
    Route::delete('/admins/{admin}', [AdminController::class, 'deleteAdmin'])->name('admin.admins.delete');
});

// Event details page (must be after specific routes like /events/create)
// Use a different pattern to avoid conflicts with resource routes
Route::get('/event-details/{slug}', [EventController::class, 'publicShow'])->name('event.details')->where('slug', '[a-z0-9\-]+');
