<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\User;
use App\Services\FileUploadService;
use App\Services\SimpleFileUploadService;
use Inertia\Inertia;
use Illuminate\Support\Str;

class EventController extends Controller
{
    /**
     * Display the public home page with featured events
     */
    public function publicHome()
    {
        // Get featured events
        $featuredEvents = Event::where('is_active', true)
            ->where('is_featured', true)
            ->where('event_date', '>=', now())
            ->with(['organizer', 'tickets' => function ($query) {
                $query->where('is_active', true)->orderBy('price');
            }])
            ->orderBy('event_date')
            ->limit(6)
            ->get();

        // Get upcoming events
        $upcomingEvents = Event::where('is_active', true)
            ->where('event_date', '>=', now())
            ->with(['organizer', 'tickets' => function ($query) {
                $query->where('is_active', true)->orderBy('price');
            }])
            ->orderBy('event_date')
            ->limit(8)
            ->get();

        // Add calculated fields
        $featuredEvents->each(function ($event) {
            $event->min_price = $event->tickets->where('is_active', true)->min('price') ?? 0;
            $event->is_free = $event->min_price == 0;
            $event->is_sold_out = $event->isSoldOut();
            $event->days_until = now()->diffInDays($event->event_date, false);
        });

        $upcomingEvents->each(function ($event) {
            $event->min_price = $event->tickets->where('is_active', true)->min('price') ?? 0;
            $event->is_free = $event->min_price == 0;
            $event->is_sold_out = $event->isSoldOut();
            $event->days_until = now()->diffInDays($event->event_date, false);
        });

        return Inertia::render('Guest/Home', [
            'featuredEvents' => $featuredEvents,
            'upcomingEvents' => $upcomingEvents,
        ]);
    }

    /**
     * Display public events listing page
     */
    public function publicIndex(Request $request)
    {
        $query = Event::where('is_active', true)
            ->where('event_date', '>=', now())
            ->with(['organizer', 'tickets' => function ($query) {
                $query->where('is_active', true)->orderBy('price');
            }]);

        // Apply filters
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('city')) {
            $query->where('city', 'like', '%' . $request->city . '%');
        }

        if ($request->filled('state')) {
            $query->where('state', 'like', '%' . $request->state . '%');
        }

        if ($request->filled('date_from')) {
            $query->where('event_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('event_date', '<=', $request->date_to);
        }

        if ($request->filled('price_range')) {
            switch ($request->price_range) {
                case 'free':
                    $query->whereHas('tickets', function ($q) {
                        $q->where('price', 0)->where('is_active', true);
                    });
                    break;
                case 'paid':
                    $query->whereHas('tickets', function ($q) {
                        $q->where('price', '>', 0)->where('is_active', true);
                    });
                    break;
            }
        }

        // Apply sorting
        $sortBy = $request->get('sort', 'date');
        switch ($sortBy) {
            case 'date':
                $query->orderBy('event_date');
                break;
            case 'popularity':
                $query->orderBy('view_count', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
        }

        $events = $query->paginate(12)->withQueryString();

        // Add calculated fields
        $events->getCollection()->transform(function ($event) {
            $event->min_price = $event->tickets->where('is_active', true)->min('price') ?? 0;
            $event->is_free = $event->min_price == 0;
            $event->is_sold_out = $event->isSoldOut();
            $event->days_until = now()->diffInDays($event->event_date, false);
            return $event;
        });

        // Get filter options
        $categories = Event::where('is_active', true)
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->filter()
            ->sort()
            ->values();

        $cities = Event::where('is_active', true)
            ->distinct()
            ->pluck('city')
            ->filter()
            ->sort()
            ->values();

        $states = Event::where('is_active', true)
            ->distinct()
            ->pluck('state')
            ->filter()
            ->sort()
            ->values();

        return Inertia::render('Guest/Events', [
            'events' => $events,
            'filters' => [
                'categories' => $categories,
                'cities' => $cities,
                'states' => $states,
            ],
            'currentFilters' => $request->only(['category', 'city', 'state', 'date_from', 'date_to', 'price_range', 'sort']),
        ]);
    }

    /**
     * Search events
     */
    public function publicSearch(Request $request)
    {
        $query = $request->get('q', '');

        if (empty($query)) {
            return redirect()->route('events.public');
        }

        $events = Event::where('is_active', true)
            ->where('event_date', '>=', now())
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', '%' . $query . '%')
                  ->orWhere('description', 'like', '%' . $query . '%')
                  ->orWhere('venue', 'like', '%' . $query . '%')
                  ->orWhere('city', 'like', '%' . $query . '%')
                  ->orWhere('category', 'like', '%' . $query . '%');
            })
            ->with(['organizer', 'tickets' => function ($query) {
                $query->where('is_active', true)->orderBy('price');
            }])
            ->orderBy('event_date')
            ->paginate(12)
            ->withQueryString();

        // Add calculated fields
        $events->getCollection()->transform(function ($event) {
            $event->min_price = $event->tickets->where('is_active', true)->min('price') ?? 0;
            $event->is_free = $event->min_price == 0;
            $event->is_sold_out = $event->isSoldOut();
            $event->days_until = now()->diffInDays($event->event_date, false);
            return $event;
        });

        return Inertia::render('Guest/SearchResults', [
            'events' => $events,
            'query' => $query,
        ]);
    }

    /**
     * Display public event details
     */
    public function publicShow($slug)
    {
        Log::info('EventController publicShow called with slug: ' . $slug);

        $event = Event::where('slug', $slug)
            ->with(['organizer', 'tickets' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }])
            ->firstOrFail();

        Log::info('Event found: ' . $event->title);

        // If event is not active, only show to owner or admin
        if (!$event->is_active) {
            $user = Auth::user();
            if (!$user || ($user->id !== $event->organizer_id && $user->role !== 'admin')) {
                abort(404, 'Event not found or not available');
            }
        }

        // Increment view count
        $event->incrementViewCount();

        // Add calculated fields
        $event->min_price = $event->tickets->where('is_active', true)->min('price') ?? 0;
        $event->is_free = $event->min_price == 0;
        $event->is_sold_out = $event->isSoldOut();
        $event->is_expired = $event->isExpired();

        // Get related events (same category or city)
        $relatedEvents = Event::where('is_active', true)
            ->where('id', '!=', $event->id)
            ->where('event_date', '>=', now())
            ->where(function ($query) use ($event) {
                $query->where('category', $event->category)
                      ->orWhere('city', $event->city);
            })
            ->with(['organizer', 'tickets' => function ($query) {
                $query->where('is_active', true)->orderBy('price');
            }])
            ->limit(3)
            ->get();

        $relatedEvents->each(function ($relatedEvent) {
            $relatedEvent->min_price = $relatedEvent->tickets->where('is_active', true)->min('price') ?? 0;
            $relatedEvent->is_free = $relatedEvent->min_price == 0;
            $relatedEvent->is_sold_out = $relatedEvent->isSoldOut();
        });

        return Inertia::render('Guest/EventDetails', [
            'event' => $event->toArray(),
            'relatedEvents' => $relatedEvents->toArray(),
        ]);
    }

    /**
     * Display user dashboard with analytics
     */
    public function dashboard()
    {
        /** @var User $user */
        $user = Auth::user();

        // Get user's events with analytics
        $events = $user->events()->with(['orders', 'tickets'])->get();

        // Calculate overall statistics
        $totalEvents = $events->count();
        $activeEvents = $events->where('is_active', true)->where('event_date', '>=', now())->count();

        // Revenue calculations
        $totalRevenue = $events->sum(function ($event) {
            return $event->getTotalRevenue();
        });

        $organizerRevenue = $events->sum(function ($event) {
            return $event->getOrganizerRevenue();
        });

        // Ticket sales
        $totalTicketsSold = $events->sum('current_attendees');

        // Recent orders
        $recentOrders = Order::whereIn('event_id', $events->pluck('id'))
            ->where('status', 'completed')
            ->with(['event', 'items.ticket'])
            ->latest()
            ->limit(10)
            ->get();

        // Monthly revenue data for charts (last 6 months)
        $monthlyRevenue = [];
        $monthlyTickets = [];
        $months = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthStart = $date->copy()->startOfMonth();
            $monthEnd = $date->copy()->endOfMonth();

            $monthRevenue = Order::whereIn('event_id', $events->pluck('id'))
                ->where('status', 'completed')
                ->whereBetween('orders.created_at', [$monthStart, $monthEnd])
                ->sum('subtotal'); // Organizer's share

            $monthTickets = Order::whereIn('event_id', $events->pluck('id'))
                ->where('status', 'completed')
                ->whereBetween('orders.created_at', [$monthStart, $monthEnd])
                ->join('order_items', 'orders.id', '=', 'order_items.order_id')
                ->sum('order_items.quantity');

            $months[] = $date->format('M');
            $monthlyRevenue[] = $monthRevenue;
            $monthlyTickets[] = $monthTickets;
        }

        // Event performance data
        $eventPerformance = $events->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'slug' => $event->slug,
                'date' => $event->event_date,
                'status' => $event->isExpired() ? 'completed' : ($event->is_active ? 'active' : 'inactive'),
                'tickets_sold' => $event->current_attendees,
                'total_capacity' => $event->max_attendees,
                'revenue' => $event->getTotalRevenue(),
                'organizer_revenue' => $event->getOrganizerRevenue(),
                'view_count' => $event->view_count,
                'orders_count' => $event->orders()->where('status', 'completed')->count(),
            ];
        })->sortByDesc('revenue')->take(5);

        // Event categories breakdown
        $categoryBreakdown = $events->groupBy('category')->map(function ($categoryEvents, $category) {
            return [
                'category' => $category ?: 'Uncategorized',
                'count' => $categoryEvents->count(),
                'revenue' => $categoryEvents->sum(function ($event) {
                    return $event->getTotalRevenue();
                }),
            ];
        })->values();

        return Inertia::render('User/Dashboard', [
            'user' => $user,
            'analytics' => [
                'total_events' => $totalEvents,
                'active_events' => $activeEvents,
                'total_revenue' => $totalRevenue,
                'organizer_revenue' => $organizerRevenue,
                'total_tickets_sold' => $totalTicketsSold,
                'monthly_revenue' => $monthlyRevenue,
                'monthly_tickets' => $monthlyTickets,
                'months' => $months,
                'event_performance' => $eventPerformance,
                'category_breakdown' => $categoryBreakdown,
                'recent_orders' => $recentOrders,
            ],
        ]);
    }
    /**
     * Display user analytics page with detailed statistics
     */
    public function analytics()
    {
        /** @var User $user */
        $user = Auth::user();

        // Get user's events with analytics
        $events = $user->events()->with(['orders', 'tickets'])->get();

        // Calculate overall statistics
        $totalEvents = $events->count();
        $activeEvents = $events->where('is_active', true)->where('event_date', '>=', now())->count();
        $completedEvents = $events->where('event_date', '<', now())->count();

        // Revenue calculations
        $totalRevenue = $events->sum(function ($event) {
            return $event->getTotalRevenue();
        });

        $organizerRevenue = $events->sum(function ($event) {
            return $event->getOrganizerRevenue();
        });

        // Ticket sales
        $totalTicketsSold = $events->sum('current_attendees');
        $totalViews = $events->sum('view_count');

        // Monthly analytics for the last 12 months
        $months = [];
        $monthlyRevenue = [];
        $monthlyTickets = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthName = $date->format('M');
            $months[] = $monthName;

            $monthRevenue = $user->events()
                ->whereMonth('event_date', $date->month)
                ->whereYear('event_date', $date->year)
                ->get()
                ->sum(function ($event) {
                    return $event->getOrganizerRevenue();
                });

            $monthTickets = $user->events()
                ->whereMonth('event_date', $date->month)
                ->whereYear('event_date', $date->year)
                ->sum('current_attendees');

            $monthlyRevenue[] = $monthRevenue;
            $monthlyTickets[] = $monthTickets;
        }

        // Event performance data
        $eventPerformance = $events->map(function ($event) {
            $attendanceRate = $event->max_attendees > 0
                ? ($event->current_attendees / $event->max_attendees) * 100
                : 0;

            return [
                'id' => $event->id,
                'title' => $event->title,
                'slug' => $event->slug,
                'date' => $event->event_date,
                'status' => $event->isExpired() ? 'completed' : ($event->is_active ? 'active' : 'inactive'),
                'tickets_sold' => $event->current_attendees,
                'total_capacity' => $event->max_attendees,
                'attendance_rate' => round($attendanceRate, 1),
                'revenue' => $event->getTotalRevenue(),
                'organizer_revenue' => $event->getOrganizerRevenue(),
                'view_count' => $event->view_count,
                'orders_count' => $event->orders()->where('status', 'completed')->count(),
            ];
        })->sortByDesc('revenue')->take(10);

        // Category breakdown
        $categoryBreakdown = $events->groupBy('category')->map(function ($categoryEvents, $category) {
            return [
                'category' => $category ?: 'Uncategorized',
                'events_count' => $categoryEvents->count(),
                'total_revenue' => $categoryEvents->sum(function ($event) {
                    return $event->getOrganizerRevenue();
                }),
                'tickets_sold' => $categoryEvents->sum('current_attendees'),
            ];
        })->values();

        // Performance insights
        $averageTicketPrice = $totalTicketsSold > 0 ? $organizerRevenue / $totalTicketsSold : 0;
        $averageAttendanceRate = $events->where('max_attendees', '>', 0)->avg(function ($event) {
            return $event->max_attendees > 0 ? ($event->current_attendees / $event->max_attendees) * 100 : 0;
        });

        // This month vs last month comparison
        $thisMonth = now();
        $lastMonth = now()->subMonth();

        $thisMonthRevenue = $user->events()
            ->whereMonth('event_date', $thisMonth->month)
            ->whereYear('event_date', $thisMonth->year)
            ->get()
            ->sum(function ($event) {
                return $event->getOrganizerRevenue();
            });

        $lastMonthRevenue = $user->events()
            ->whereMonth('event_date', $lastMonth->month)
            ->whereYear('event_date', $lastMonth->year)
            ->get()
            ->sum(function ($event) {
                return $event->getOrganizerRevenue();
            });

        return Inertia::render('User/Analytics', [
            'user' => $user,
            'analytics' => [
                'total_events' => $totalEvents,
                'active_events' => $activeEvents,
                'completed_events' => $completedEvents,
                'total_revenue' => $totalRevenue,
                'organizer_revenue' => $organizerRevenue,
                'total_tickets_sold' => $totalTicketsSold,
                'total_views' => $totalViews,
                'this_month_revenue' => $thisMonthRevenue,
                'last_month_revenue' => $lastMonthRevenue,
                'monthly_revenue' => $monthlyRevenue,
                'monthly_tickets' => $monthlyTickets,
                'months' => $months,
                'event_performance' => $eventPerformance,
                'category_breakdown' => $categoryBreakdown,
                'average_ticket_price' => $averageTicketPrice,
                'average_attendance_rate' => $averageAttendanceRate ?: 0,
            ],
        ]);
    }

    /**
     * Display a listing of user's events
     */
    public function index()
    {
        /** @var User $user */
        $user = Auth::user();

        $events = $user->events()
            ->with(['tickets'])
            ->withCount(['orders as total_orders', 'orders as completed_orders' => function ($query) {
                $query->where('status', 'completed');
            }])
            ->latest()
            ->paginate(10);

        // Add calculated fields
        $events->getCollection()->transform(function ($event) {
            $event->total_revenue = $event->getTotalRevenue();
            $event->organizer_revenue = $event->getOrganizerRevenue();
            $event->is_expired = $event->isExpired();
            $event->is_sold_out = $event->isSoldOut();
            return $event;
        });

        return Inertia::render('User/MyEvents', [
            'events' => $events,
            'user' => $user
        ]);
    }

    /**
     * Show the form for creating a new event
     */
    public function create()
    {
        return Inertia::render('User/AddEvent', [
            'user' => Auth::user()
        ]);
    }

    /**
     * Store a newly created event
     */
    public function store(Request $request)
    {
        Log::info('Event store method called', ['user_id' => Auth::id()]);
        Log::info('Request data', $request->all());
        Log::info('Ticket types in request', ['ticket_types' => $request->get('ticket_types')]);
        Log::info('Social links in request', ['social_links' => $request->get('social_links')]);
        Log::info('Boolean fields in request', [
            'requires_approval' => $request->get('requires_approval'),
            'is_active' => $request->get('is_active')
        ]);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'event_date' => 'required|date|after:today',
            'event_time' => 'required|date_format:H:i',
            'end_date' => 'nullable|date|after_or_equal:event_date',
            'end_time' => 'nullable|date_format:H:i',
            'venue' => 'required|string|max:255',
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'category' => 'nullable|string|max:100',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'terms_conditions' => 'nullable|string',
            'additional_info' => 'nullable|string',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'website_url' => 'nullable|url',
            'social_links' => 'nullable|string',
            'max_attendees' => 'nullable|integer|min:1',
            'requires_approval' => 'nullable|string',
            'custom_fields' => 'nullable|string',
            'page_customization' => 'nullable|string',
            'seo_title' => 'nullable|string|max:255',
            'seo_description' => 'nullable|string|max:500',
            'is_active' => 'nullable|string',
            'event_type' => 'nullable|in:public,private',
            'tags' => 'nullable|string',
            'ticket_types' => 'nullable|string',
        ]);

        /** @var User $user */
        $user = Auth::user();

        // Use SimpleFileUploadService if GD extension is not available
        $fileUploadService = extension_loaded('gd') ? new FileUploadService() : new SimpleFileUploadService();

        // Handle banner image upload with optimization
        if ($request->hasFile('banner_image')) {
            try {
                $validated['banner_image'] = $fileUploadService->uploadImage(
                    $request->file('banner_image'),
                    'events/banners',
                    [
                        'max_width' => 1920,
                        'max_height' => 1080,
                        'quality' => 85,
                        'create_thumbnail' => true,
                        'thumbnail_width' => 400,
                        'thumbnail_height' => 225,
                    ]
                );
            } catch (\Exception $e) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['banner_image' => 'Failed to upload banner image: ' . $e->getMessage()]);
            }
        }

        // Handle gallery images upload with optimization
        if ($request->hasFile('gallery_images')) {
            try {
                $validated['gallery_images'] = $fileUploadService->uploadMultipleImages(
                    $request->file('gallery_images'),
                    'events/gallery',
                    [
                        'max_width' => 1200,
                        'max_height' => 800,
                        'quality' => 80,
                        'create_thumbnail' => true,
                        'thumbnail_width' => 300,
                        'thumbnail_height' => 200,
                    ]
                );
            } catch (\Exception $e) {
                // Clean up banner image if gallery upload fails
                if (isset($validated['banner_image'])) {
                    $fileUploadService->deleteFile($validated['banner_image']);
                }

                return redirect()->back()
                    ->withInput()
                    ->withErrors(['gallery_images' => 'Failed to upload gallery images: ' . $e->getMessage()]);
            }
        }

        // Generate unique slug
        $baseSlug = Str::slug($validated['title']);
        $slug = $baseSlug;
        $counter = 1;

        while (Event::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        $validated['slug'] = $slug;
        $validated['organizer_id'] = $user->id;

        // Set default values
        $validated['event_type'] = $validated['event_type'] ?? 'public';
        $validated['is_active'] = $validated['is_active'] ?? true;

        // Handle tags
        if (isset($validated['tags']) && !empty($validated['tags'])) {
            $tags = json_decode($validated['tags'], true);
            if (is_array($tags)) {
                $validated['tags'] = $tags;
            } else {
                unset($validated['tags']);
            }
        } else {
            unset($validated['tags']);
        }

        // Handle social_links
        if (isset($validated['social_links']) && !empty($validated['social_links'])) {
            $socialLinks = json_decode($validated['social_links'], true);
            if (is_array($socialLinks)) {
                $validated['social_links'] = $socialLinks;
            } else {
                unset($validated['social_links']);
            }
        } else {
            $validated['social_links'] = [];
        }

        // Handle custom_fields
        if (isset($validated['custom_fields']) && !empty($validated['custom_fields'])) {
            $customFields = json_decode($validated['custom_fields'], true);
            if (is_array($customFields)) {
                $validated['custom_fields'] = $customFields;
            } else {
                unset($validated['custom_fields']);
            }
        } else {
            $validated['custom_fields'] = [];
        }

        // Handle page_customization
        if (isset($validated['page_customization']) && !empty($validated['page_customization'])) {
            $pageCustomization = json_decode($validated['page_customization'], true);
            if (is_array($pageCustomization)) {
                $validated['page_customization'] = $pageCustomization;
            } else {
                unset($validated['page_customization']);
            }
        } else {
            $validated['page_customization'] = [];
        }

        // Handle boolean fields that come as strings from FormData
        if (isset($validated['requires_approval'])) {
            $validated['requires_approval'] = filter_var($validated['requires_approval'], FILTER_VALIDATE_BOOLEAN);
        }

        if (isset($validated['is_active'])) {
            $validated['is_active'] = filter_var($validated['is_active'], FILTER_VALIDATE_BOOLEAN);
        }

        // Handle ticket types (store separately, don't include in event creation)
        $ticketTypesData = null;
        Log::info('Checking for ticket_types in request', ['ticket_types' => $request->get('ticket_types')]);

        if (isset($validated['ticket_types']) && !empty($validated['ticket_types'])) {
            $ticketTypesData = json_decode($validated['ticket_types'], true);
            Log::info('Decoded ticket types data', ['data' => $ticketTypesData]);
            unset($validated['ticket_types']); // Remove from event data
        } else {
            Log::info('No ticket_types found in validated data');
        }

        $event = Event::create($validated);
        Log::info('Event created with ID: ' . $event->id);

        // Create ticket types if provided
        if ($ticketTypesData && is_array($ticketTypesData)) {
            Log::info('Creating tickets for event', ['event_id' => $event->id, 'ticket_count' => count($ticketTypesData)]);

            foreach ($ticketTypesData as $index => $ticketData) {
                Log::info('Processing ticket data', ['index' => $index, 'data' => $ticketData]);

                if (!empty($ticketData['name']) && isset($ticketData['price'])) {
                    try {
                        $ticket = $event->tickets()->create([
                            'name' => $ticketData['name'],
                            'description' => $ticketData['description'] ?? '',
                            'price' => (float) $ticketData['price'],
                            'quantity_available' => !empty($ticketData['quantity']) ? (int) $ticketData['quantity'] : null,
                            'quantity_sold' => 0,
                            'min_purchase' => 1,
                            'max_purchase' => 10,
                            'is_active' => $ticketData['is_active'] ?? true,
                            'ticket_type' => $ticketData['type'] ?? 'general',
                            'sort_order' => $ticketData['sort_order'] ?? 1,
                        ]);
                        Log::info('Ticket created successfully', ['ticket_id' => $ticket->id, 'name' => $ticket->name]);
                    } catch (\Exception $e) {
                        Log::error('Failed to create ticket', ['error' => $e->getMessage(), 'ticket_data' => $ticketData]);
                    }
                } else {
                    Log::warning('Skipping ticket due to missing name or price', ['ticket_data' => $ticketData]);
                }
            }
        } else {
            Log::info('No ticket types data to process', ['ticketTypesData' => $ticketTypesData]);
        }

        return Inertia::render('User/AddEvent', [
            'user' => Auth::user(),
            'flash' => [
                'success' => 'Event created successfully! Your ticket types have been set up.',
                'event_slug' => $event->slug,
                'show_success_modal' => true
            ]
        ]);
    }

    /**
     * Display the specified event
     */
    public function show(Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        $event->load(['tickets', 'orders.items']);

        // Calculate analytics
        $analytics = [
            'total_revenue' => $event->getTotalRevenue(),
            'organizer_revenue' => $event->getOrganizerRevenue(),
            'platform_commission' => $event->getPlatformCommission(),
            'total_tickets_sold' => $event->orders()->where('status', 'completed')->sum('total_amount'),
            'total_orders' => $event->orders()->where('status', 'completed')->count(),
            'pending_orders' => $event->orders()->where('status', 'pending')->count(),
            'view_count' => $event->view_count,
        ];

        return Inertia::render('User/EventDetails', [
            'event' => $event,
            'analytics' => $analytics,
            'user' => Auth::user()
        ]);
    }

    /**
     * Show the form for editing the specified event
     */
    public function edit(Event $event)
    {
        Log::info('Edit event called for event ID: ' . $event->id . ' by user: ' . Auth::id());
        Log::info('Event organizer ID: ' . $event->organizer_id);
        Log::info('Current user ID: ' . Auth::id());
        Log::info('User authenticated: ' . (Auth::check() ? 'Yes' : 'No'));

        // Check if user is authenticated
        if (!Auth::check()) {
            Log::warning('Unauthenticated edit attempt for event ' . $event->id);
            return redirect()->route('login');
        }

        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            Log::warning('Unauthorized edit attempt for event ' . $event->id . ' by user ' . Auth::id());
            abort(403, 'Unauthorized access to this event.');
        }

        // Load event with tickets and tags
        $event->load('tickets');

        Log::info('Successfully loading edit page for event: ' . $event->title);

        return Inertia::render('User/EditEvent', [
            'event' => $event,
            'user' => Auth::user()
        ]);
    }

    /**
     * Update the specified event
     */
    public function update(Request $request, Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'event_date' => 'required|date',
            'event_time' => 'required|date_format:H:i',
            'end_date' => 'nullable|date|after_or_equal:event_date',
            'end_time' => 'nullable|date_format:H:i',
            'venue' => 'required|string|max:255',
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'event_type' => 'required|in:public,private',
            'category' => 'nullable|string|max:100',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'terms_conditions' => 'nullable|string',
            'additional_info' => 'nullable|string',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'website_url' => 'nullable|url',
            'social_links' => 'nullable|string',
            'max_attendees' => 'nullable|integer|min:1',
            'requires_approval' => 'nullable|string',
            'custom_fields' => 'nullable|string',
            'page_customization' => 'nullable|string',
            'seo_title' => 'nullable|string|max:255',
            'seo_description' => 'nullable|string|max:500',
            'is_active' => 'nullable|string',
            'tags' => 'nullable|string',
            'ticket_types' => 'nullable|string',
        ]);

        $fileUploadService = extension_loaded('gd') ? new FileUploadService() : new SimpleFileUploadService();

        // Handle banner image upload
        if ($request->hasFile('banner_image')) {
            try {
                // Delete old banner if exists
                if ($event->banner_image) {
                    $fileUploadService->deleteFile($event->banner_image);
                }

                $validated['banner_image'] = $fileUploadService->uploadImage(
                    $request->file('banner_image'),
                    'events/banners',
                    [
                        'max_width' => 1920,
                        'max_height' => 1080,
                        'quality' => 85,
                        'create_thumbnail' => true,
                        'thumbnail_width' => 400,
                        'thumbnail_height' => 225,
                    ]
                );
            } catch (\Exception $e) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['banner_image' => 'Failed to upload banner image: ' . $e->getMessage()]);
            }
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            try {
                // Delete old gallery images if exists
                if ($event->gallery_images) {
                    $fileUploadService->deleteMultipleFiles($event->gallery_images);
                }

                $validated['gallery_images'] = $fileUploadService->uploadMultipleImages(
                    $request->file('gallery_images'),
                    'events/gallery',
                    [
                        'max_width' => 1200,
                        'max_height' => 800,
                        'quality' => 80,
                        'create_thumbnail' => true,
                        'thumbnail_width' => 300,
                        'thumbnail_height' => 200,
                    ]
                );
            } catch (\Exception $e) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['gallery_images' => 'Failed to upload gallery images: ' . $e->getMessage()]);
            }
        }

        // Handle tags
        if (isset($validated['tags']) && !empty($validated['tags'])) {
            $tags = json_decode($validated['tags'], true);
            if (is_array($tags)) {
                $validated['tags'] = $tags;
            } else {
                unset($validated['tags']);
            }
        } else {
            unset($validated['tags']);
        }

        // Handle social_links
        if (isset($validated['social_links']) && !empty($validated['social_links'])) {
            $socialLinks = json_decode($validated['social_links'], true);
            if (is_array($socialLinks)) {
                $validated['social_links'] = $socialLinks;
            } else {
                unset($validated['social_links']);
            }
        } else {
            $validated['social_links'] = [];
        }

        // Handle custom_fields
        if (isset($validated['custom_fields']) && !empty($validated['custom_fields'])) {
            $customFields = json_decode($validated['custom_fields'], true);
            if (is_array($customFields)) {
                $validated['custom_fields'] = $customFields;
            } else {
                unset($validated['custom_fields']);
            }
        } else {
            $validated['custom_fields'] = [];
        }

        // Handle page_customization
        if (isset($validated['page_customization']) && !empty($validated['page_customization'])) {
            $pageCustomization = json_decode($validated['page_customization'], true);
            if (is_array($pageCustomization)) {
                $validated['page_customization'] = $pageCustomization;
            } else {
                unset($validated['page_customization']);
            }
        } else {
            $validated['page_customization'] = [];
        }

        // Handle boolean fields that come as strings from FormData
        if (isset($validated['requires_approval'])) {
            $validated['requires_approval'] = filter_var($validated['requires_approval'], FILTER_VALIDATE_BOOLEAN);
        }

        if (isset($validated['is_active'])) {
            $validated['is_active'] = filter_var($validated['is_active'], FILTER_VALIDATE_BOOLEAN);
        }

        // Handle ticket types (update existing tickets)
        $ticketTypesData = null;
        Log::info('Checking for ticket_types in update request', ['ticket_types' => $request->get('ticket_types')]);

        if (isset($validated['ticket_types']) && !empty($validated['ticket_types'])) {
            $ticketTypesData = json_decode($validated['ticket_types'], true);
            Log::info('Decoded ticket types data for update', ['data' => $ticketTypesData]);
            unset($validated['ticket_types']); // Remove from event data
        } else {
            Log::info('No ticket_types found in validated data for update');
        }

        $event->update($validated);
        Log::info('Event updated with ID: ' . $event->id);

        // Update ticket types if provided
        if ($ticketTypesData && is_array($ticketTypesData)) {
            Log::info('Updating tickets for event', ['event_id' => $event->id, 'ticket_count' => count($ticketTypesData)]);

            // Get existing tickets
            $existingTickets = $event->tickets()->get()->keyBy('id');
            $updatedTicketIds = [];

            foreach ($ticketTypesData as $index => $ticketData) {
                Log::info('Processing ticket data for update', ['index' => $index, 'data' => $ticketData]);

                if (!empty($ticketData['name']) && isset($ticketData['price'])) {
                    try {
                        $ticketAttributes = [
                            'name' => $ticketData['name'],
                            'description' => $ticketData['description'] ?? '',
                            'price' => (float) $ticketData['price'],
                            'quantity_available' => !empty($ticketData['quantity']) ? (int) $ticketData['quantity'] : null,
                            'min_purchase' => 1,
                            'max_purchase' => 10,
                            'is_active' => $ticketData['is_active'] ?? true,
                            'ticket_type' => $ticketData['type'] ?? 'general',
                            'sort_order' => $ticketData['sort_order'] ?? 1,
                        ];

                        if (isset($ticketData['id']) && $existingTickets->has($ticketData['id'])) {
                            // Update existing ticket
                            $ticket = $existingTickets->get($ticketData['id']);
                            $ticket->update($ticketAttributes);
                            $updatedTicketIds[] = $ticket->id;
                            Log::info('Ticket updated successfully', ['ticket_id' => $ticket->id, 'name' => $ticket->name]);
                        } else {
                            // Create new ticket
                            $ticket = $event->tickets()->create($ticketAttributes);
                            $updatedTicketIds[] = $ticket->id;
                            Log::info('New ticket created successfully', ['ticket_id' => $ticket->id, 'name' => $ticket->name]);
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to update/create ticket', ['error' => $e->getMessage(), 'ticket_data' => $ticketData]);
                    }
                } else {
                    Log::warning('Skipping ticket due to missing name or price', ['ticket_data' => $ticketData]);
                }
            }

            // Delete tickets that are no longer in the update
            $ticketsToDelete = $existingTickets->whereNotIn('id', $updatedTicketIds);
            foreach ($ticketsToDelete as $ticket) {
                Log::info('Deleting removed ticket', ['ticket_id' => $ticket->id, 'name' => $ticket->name]);
                $ticket->delete();
            }
        } else {
            Log::info('No ticket types data to process for update', ['ticketTypesData' => $ticketTypesData]);
        }

        return redirect()->route('events.index')
            ->with('success', 'Event updated successfully!');
    }

    /**
     * Remove the specified event
     */
    public function destroy(Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        // Check if event has orders
        if ($event->orders()->where('status', 'completed')->exists()) {
            throw ValidationException::withMessages([
                'event' => 'Cannot delete event with completed orders. Please contact support.'
            ]);
        }

        // Delete associated files using FileUploadService
        $fileUploadService = extension_loaded('gd') ? new FileUploadService() : new SimpleFileUploadService();

        if ($event->banner_image) {
            $fileUploadService->deleteFile($event->banner_image);
        }

        if ($event->gallery_images) {
            $fileUploadService->deleteMultipleFiles($event->gallery_images);
        }

        $event->delete();

        return redirect()->route('events.index')
            ->with('success', 'Event deleted successfully!');
    }

    /**
     * Show event management page for organizers
     */
    public function manage(Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        // Get all attendees (orders with completed status)
        $attendees = $event->orders()
            ->where('status', 'completed')
            ->with(['items.ticket'])
            ->get()
            ->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'buyer_name' => $order->buyer_name,
                    'buyer_email' => $order->buyer_email,
                    'buyer_phone' => $order->buyer_phone,
                    'total_amount' => $order->total_amount,
                    'tickets_count' => $order->items->sum('quantity'),
                    'checked_in' => $order->checked_in ?? false,
                    'checked_in_at' => $order->checked_in_at,
                    'created_at' => $order->created_at,
                ];
            });

        // Calculate stats
        $stats = [
            'total_attendees' => $attendees->count(),
            'checked_in' => $attendees->where('checked_in', true)->count(),
            'pending' => $attendees->where('checked_in', false)->count(),
        ];

        return Inertia::render('User/ManageEvent', [
            'event' => $event,
            'attendees' => $attendees,
            'stats' => $stats,
            'user' => Auth::user()
        ]);
    }

    /**
     * Handle attendee check-in/check-out
     */
    public function checkInAttendee(Request $request, Event $event, Order $order)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        // Validate the order belongs to this event
        if ($order->event_id !== $event->id) {
            abort(404, 'Order not found for this event.');
        }

        $validated = $request->validate([
            'action' => 'required|in:check-in,check-out'
        ]);

        if ($validated['action'] === 'check-in') {
            $order->update([
                'checked_in' => true,
                'checked_in_at' => now()
            ]);
        } else {
            $order->update([
                'checked_in' => false,
                'checked_in_at' => null
            ]);
        }

        return redirect()->back()
            ->with('success', 'Attendee status updated successfully.');
    }

    /**
     * Verify ticket by QR code or order number
     */
    public function verifyTicket(Request $request, Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        $validated = $request->validate([
            'code' => 'required|string'
        ]);

        // Try to find order by QR code or order number
        $order = $event->orders()
            ->where('status', 'completed')
            ->where(function ($query) use ($validated) {
                $query->where('qr_code', $validated['code'])
                      ->orWhere('order_number', $validated['code']);
            })
            ->first();

        if (!$order) {
            return redirect()->back()
                ->withErrors(['code' => 'Invalid ticket code. Please check and try again.']);
        }

        if ($order->checked_in) {
            return redirect()->back()
                ->withErrors(['code' => 'This ticket has already been checked in.']);
        }

        // Check in the attendee
        $order->update([
            'checked_in' => true,
            'checked_in_at' => now()
        ]);

        return redirect()->back()
            ->with('success', "Ticket verified! {$order->buyer_name} has been checked in.");
    }
}
