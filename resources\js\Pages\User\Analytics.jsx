import React from 'react';
import { Head } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  EyeIcon,
  TicketIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import AppLayout from '../../Layouts/AppLayout';

const Analytics = ({ user, analytics }) => {
  // Format currency
  const formatCurrency = (amount) => {
    return `₦${Number(amount).toLocaleString()}`;
  };

  // Calculate revenue growth
  const revenueGrowth = analytics.last_month_revenue > 0
    ? (((analytics.this_month_revenue - analytics.last_month_revenue) / analytics.last_month_revenue) * 100).toFixed(1)
    : analytics.this_month_revenue > 0 ? 100 : 0;

  // Prepare chart data from backend analytics
  const chartData = analytics.months?.map((month, index) => ({
    month,
    revenue: analytics.monthly_revenue[index] || 0,
    tickets: analytics.monthly_tickets[index] || 0,
  })) || [];

  // Get max values for chart scaling
  const maxRevenue = Math.max(...analytics.monthly_revenue) || 1;
  const maxTickets = Math.max(...analytics.monthly_tickets) || 1;

  return (
    <AppLayout user={user}>
      <Head title="Analytics - TickGet" />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex-1 min-w-0">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
              Analytics Dashboard
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Track your event performance and revenue insights
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CalendarIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Events</dt>
                    <dd className="text-lg font-medium text-gray-900">{analytics.total_events}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TicketIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Tickets Sold</dt>
                    <dd className="text-lg font-medium text-gray-900">{analytics.total_tickets_sold.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(analytics.organizer_revenue)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <EyeIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Views</dt>
                    <dd className="text-lg font-medium text-gray-900">{analytics.total_views.toLocaleString()}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Revenue Growth Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white overflow-hidden shadow rounded-lg"
        >
          <div className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ArrowTrendingUpIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-5">
                <h3 className="text-lg font-medium text-gray-900">Revenue Growth</h3>
                <div className="mt-2 flex items-baseline">
                  <p className="text-2xl font-semibold text-green-600">+{revenueGrowth}%</p>
                  <p className="ml-2 text-sm text-gray-500">from last month</p>
                </div>
                <div className="mt-1">
                  <p className="text-sm text-gray-600">
                    This month: {formatCurrency(analytics.this_month_revenue)} •
                    Last month: {formatCurrency(analytics.last_month_revenue)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Revenue</h3>
              <div className="space-y-3">
                {chartData.map((item, index) => (
                  <div key={item.month} className="flex items-center">
                    <div className="w-12 text-sm text-gray-500">{item.month}</div>
                    <div className="flex-1 mx-4">
                      <div className="bg-gray-200 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${maxRevenue > 0 ? (item.revenue / maxRevenue) * 100 : 0}%` }}
                          transition={{ delay: 0.8 + index * 0.1, duration: 0.5 }}
                          className="bg-indigo-600 h-2 rounded-full"
                        />
                      </div>
                    </div>
                    <div className="w-20 text-sm text-gray-900 text-right">
                      ₦{(item.revenue / 1000).toFixed(0)}k
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Tickets Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Tickets Sold</h3>
              <div className="space-y-3">
                {chartData.map((item, index) => (
                  <div key={item.month} className="flex items-center">
                    <div className="w-12 text-sm text-gray-500">{item.month}</div>
                    <div className="flex-1 mx-4">
                      <div className="bg-gray-200 rounded-full h-2">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${maxTickets > 0 ? (item.tickets / maxTickets) * 100 : 0}%` }}
                          transition={{ delay: 1.0 + index * 0.1, duration: 0.5 }}
                          className="bg-green-600 h-2 rounded-full"
                        />
                      </div>
                    </div>
                    <div className="w-16 text-sm text-gray-900 text-right">
                      {item.tickets}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Performance Insights */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="bg-white overflow-hidden shadow rounded-lg"
        >
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Insights</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{analytics.average_attendance_rate.toFixed(1)}%</div>
                <div className="text-sm text-blue-800">Average Attendance Rate</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{formatCurrency(analytics.average_ticket_price)}</div>
                <div className="text-sm text-green-800">Average Ticket Price</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{analytics.active_events}</div>
                <div className="text-sm text-purple-800">Active Events</div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </AppLayout>
  );
};

export default Analytics;
