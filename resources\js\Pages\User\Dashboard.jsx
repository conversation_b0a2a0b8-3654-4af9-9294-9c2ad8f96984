import React, { useState, useEffect } from 'react';
import { Head, usePage } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  CurrencyDollarIcon,
  TicketIcon,
  ChartBarIcon,
  PlusIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import AppLayout from '../../Layouts/AppLayout';
import SuccessAlert from '../../Components/Notifications/SuccessAlert';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const Dashboard = ({ user, analytics }) => {
  const { flash } = usePage().props;
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Handle flash messages
  useEffect(() => {
    if (flash?.success) {
      setSuccessMessage(flash.success);
      setShowSuccessAlert(true);
    }
  }, [flash]);

  // Format currency
  const formatCurrency = (amount) => {
    return `₦${Number(amount).toLocaleString()}`;
  };

  // Real data from backend
  const stats = [
    {
      name: 'Total Events',
      value: analytics.total_events.toString(),
      change: analytics.total_events > 0 ? `+${analytics.total_events}` : '0',
      changeType: 'increase',
      icon: CalendarIcon,
    },
    {
      name: 'Total Revenue',
      value: formatCurrency(analytics.organizer_revenue),
      change: analytics.organizer_revenue > 0 ? '+' + Math.round((analytics.organizer_revenue / Math.max(analytics.total_revenue, 1)) * 100) + '%' : '0%',
      changeType: 'increase',
      icon: CurrencyDollarIcon,
    },
    {
      name: 'Tickets Sold',
      value: analytics.total_tickets_sold.toLocaleString(),
      change: analytics.total_tickets_sold > 0 ? `+${analytics.total_tickets_sold}` : '0',
      changeType: 'increase',
      icon: TicketIcon,
    },
    {
      name: 'Active Events',
      value: analytics.active_events.toString(),
      change: analytics.active_events > 0 ? `+${analytics.active_events}` : '0',
      changeType: 'increase',
      icon: ChartBarIcon,
    },
  ];

  // Use real event performance data
  const recentEvents = analytics.event_performance || [];

  // Chart data using real analytics
  const salesData = {
    labels: analytics.months || [],
    datasets: [
      {
        label: 'Ticket Sales',
        data: analytics.monthly_tickets || [],
        borderColor: 'rgb(79, 70, 229)',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        tension: 0.4,
      },
    ],
  };

  const revenueData = {
    labels: analytics.months || [],
    datasets: [
      {
        label: 'Revenue (₦)',
        data: analytics.monthly_revenue || [],
        backgroundColor: 'rgba(79, 70, 229, 0.8)',
      },
    ],
  };

  const eventTypeData = {
    labels: analytics.category_breakdown?.map(cat => cat.category) || [],
    datasets: [
      {
        data: analytics.category_breakdown?.map(cat => cat.count) || [],
        backgroundColor: [
          'rgba(79, 70, 229, 0.8)',
          'rgba(139, 92, 246, 0.8)',
          'rgba(236, 72, 153, 0.8)',
          'rgba(34, 197, 94, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
        ],
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
    },
  };

  return (
    <AppLayout user={user}>
      <Head title="Dashboard - TickGet" />
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back! Here's what's happening with your events.</p>
          </div>
          <motion.a
            href="/events/create"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Create Event
          </motion.a>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className="absolute bg-indigo-500 rounded-md p-3">
                  <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
                <p className="ml-16 text-sm font-medium text-gray-500 truncate">{stat.name}</p>
              </dt>
              <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                <p
                  className={`ml-2 flex items-baseline text-sm font-semibold ${
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {stat.change}
                </p>
              </dd>
            </motion.div>
          ))}
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white p-6 rounded-lg shadow"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Ticket Sales Trend</h3>
            <Line data={salesData} options={chartOptions} />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white p-6 rounded-lg shadow"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Revenue</h3>
            <Bar data={revenueData} options={chartOptions} />
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white p-6 rounded-lg shadow"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Event Types</h3>
            <Doughnut data={eventTypeData} options={chartOptions} />
          </motion.div>

          {/* Recent Events */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="lg:col-span-2 bg-white shadow rounded-lg"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Events</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {recentEvents.length > 0 ? recentEvents.map((event) => (
                <div key={event.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium text-gray-900">{event.title}</h4>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            event.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : event.status === 'completed'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {event.status}
                        </span>
                      </div>
                      <div className="mt-1 flex items-center text-sm text-gray-500">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        {new Date(event.date).toLocaleDateString()}
                        <span className="mx-2">•</span>
                        <span>{event.tickets_sold}{event.total_capacity ? `/${event.total_capacity}` : ''} tickets sold</span>
                        <span className="mx-2">•</span>
                        <span className="text-green-600 font-medium">{formatCurrency(event.organizer_revenue)}</span>
                      </div>
                    </div>
                    <motion.a
                      href={`/event-details/${event.slug}`}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="ml-4 inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <EyeIcon className="h-4 w-4 mr-1" />
                      View
                    </motion.a>
                  </div>
                </div>
              )) : (
                <div className="px-6 py-8 text-center">
                  <CalendarIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No events yet</h3>
                  <p className="mt-1 text-sm text-gray-500">Get started by creating your first event.</p>
                  <div className="mt-6">
                    <motion.a
                      href="/events/create"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      Create Event
                    </motion.a>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Success Alert */}
      <SuccessAlert
        show={showSuccessAlert}
        onClose={() => setShowSuccessAlert(false)}
        title="Welcome!"
        message={successMessage}
      />
    </AppLayout>
  );
};

export default Dashboard;
