<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class FileUploadService
{
    /**
     * Upload and optimize an image
     */
    public function uploadImage(UploadedFile $file, string $directory = 'uploads', array $options = []): string
    {
        // Default options
        $options = array_merge([
            'max_width' => 1920,
            'max_height' => 1080,
            'quality' => 85,
            'create_thumbnail' => false,
            'thumbnail_width' => 300,
            'thumbnail_height' => 200,
        ], $options);

        // Validate file
        $this->validateImage($file);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        $path = $directory . '/' . $filename;

        try {
            // Check if GD extension is available
            if (!extension_loaded('gd')) {
                // Fallback: Just store the file without processing
                Storage::disk('public')->put($path, file_get_contents($file->getRealPath()));
            } else {
                // Get image info
                $imageInfo = getimagesize($file->getRealPath());
                [$originalWidth, $originalHeight, $imageType] = $imageInfo;

                // Create image resource from file
                $sourceImage = $this->createImageFromFile($file->getRealPath(), $imageType);

                // Calculate new dimensions
                [$newWidth, $newHeight] = $this->calculateDimensions(
                    $originalWidth,
                    $originalHeight,
                    $options['max_width'],
                    $options['max_height']
                );

                // Create new image with calculated dimensions
                $resizedImage = imagecreatetruecolor($newWidth, $newHeight);

                // Preserve transparency for PNG and GIF
                if ($imageType === IMAGETYPE_PNG || $imageType === IMAGETYPE_GIF) {
                    imagealphablending($resizedImage, false);
                    imagesavealpha($resizedImage, true);
                    $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
                    imagefill($resizedImage, 0, 0, $transparent);
                }

                // Resize the image
                imagecopyresampled(
                    $resizedImage, $sourceImage,
                    0, 0, 0, 0,
                    $newWidth, $newHeight,
                    $originalWidth, $originalHeight
                );

                // Save the optimized image
                $tempPath = tempnam(sys_get_temp_dir(), 'upload_');
                $this->saveImageToFile($resizedImage, $tempPath, $imageType, $options['quality']);

                // Store the file
                Storage::disk('public')->put($path, file_get_contents($tempPath));
                unlink($tempPath);

                // Create thumbnail if requested
                if ($options['create_thumbnail']) {
                    $this->createThumbnail($resizedImage, $directory, $filename, $options, $imageType);
                }

                // Clean up memory
                imagedestroy($sourceImage);
                imagedestroy($resizedImage);
            }

            Log::info('Image uploaded successfully', [
                'original_name' => $file->getClientOriginalName(),
                'stored_path' => $path,
                'file_size' => $file->getSize(),
            ]);

            return $path;

        } catch (\Exception $e) {
            Log::error('Image upload failed', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName(),
            ]);

            throw new \Exception('Failed to upload and process image: ' . $e->getMessage());
        }
    }

    /**
     * Upload multiple images
     */
    public function uploadMultipleImages(array $files, string $directory = 'uploads', array $options = []): array
    {
        $uploadedPaths = [];

        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $uploadedPaths[] = $this->uploadImage($file, $directory, $options);
            }
        }

        return $uploadedPaths;
    }

    /**
     * Delete an uploaded file
     */
    public function deleteFile(string $path): bool
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                
                // Also delete thumbnail if exists
                $thumbnailPath = $this->getThumbnailPath($path);
                if (Storage::disk('public')->exists($thumbnailPath)) {
                    Storage::disk('public')->delete($thumbnailPath);
                }

                Log::info('File deleted successfully', ['path' => $path]);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('File deletion failed', [
                'error' => $e->getMessage(),
                'path' => $path,
            ]);

            return false;
        }
    }

    /**
     * Delete multiple files
     */
    public function deleteMultipleFiles(array $paths): array
    {
        $results = [];

        foreach ($paths as $path) {
            $results[$path] = $this->deleteFile($path);
        }

        return $results;
    }

    /**
     * Validate uploaded image
     */
    protected function validateImage(UploadedFile $file): void
    {
        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            throw new \Exception('File size too large. Maximum allowed size is 5MB.');
        }

        // Check file type
        $allowedMimes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \Exception('Invalid file type. Only JPEG, PNG, JPG, GIF, and WebP images are allowed.');
        }

        // Check image dimensions
        $imageInfo = getimagesize($file->getRealPath());
        if (!$imageInfo) {
            throw new \Exception('Invalid image file.');
        }

        [$width, $height] = $imageInfo;
        
        // Minimum dimensions
        if ($width < 100 || $height < 100) {
            throw new \Exception('Image dimensions too small. Minimum size is 100x100 pixels.');
        }

        // Maximum dimensions
        if ($width > 5000 || $height > 5000) {
            throw new \Exception('Image dimensions too large. Maximum size is 5000x5000 pixels.');
        }
    }

    /**
     * Generate unique filename
     */
    protected function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d-H-i-s');
        $random = Str::random(8);
        
        return $timestamp . '-' . $random . '.' . $extension;
    }

    /**
     * Create image resource from file
     */
    protected function createImageFromFile(string $filePath, int $imageType)
    {
        if (!extension_loaded('gd')) {
            throw new \Exception('GD extension is not available for image processing');
        }

        switch ($imageType) {
            case IMAGETYPE_JPEG:
                return imagecreatefromjpeg($filePath);
            case IMAGETYPE_PNG:
                return imagecreatefrompng($filePath);
            case IMAGETYPE_GIF:
                return imagecreatefromgif($filePath);
            case IMAGETYPE_WEBP:
                if (function_exists('imagecreatefromwebp')) {
                    return imagecreatefromwebp($filePath);
                }
                throw new \Exception('WebP support not available');
            default:
                throw new \Exception('Unsupported image type');
        }
    }

    /**
     * Calculate new dimensions maintaining aspect ratio
     */
    protected function calculateDimensions(int $originalWidth, int $originalHeight, int $maxWidth, int $maxHeight): array
    {
        if ($originalWidth <= $maxWidth && $originalHeight <= $maxHeight) {
            return [$originalWidth, $originalHeight];
        }

        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);

        return [
            (int) round($originalWidth * $ratio),
            (int) round($originalHeight * $ratio)
        ];
    }

    /**
     * Save image resource to file
     */
    protected function saveImageToFile($imageResource, string $filePath, int $imageType, int $quality): void
    {
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                imagejpeg($imageResource, $filePath, $quality);
                break;
            case IMAGETYPE_PNG:
                // PNG quality is 0-9, convert from 0-100
                $pngQuality = (int) round((100 - $quality) / 10);
                imagepng($imageResource, $filePath, $pngQuality);
                break;
            case IMAGETYPE_GIF:
                imagegif($imageResource, $filePath);
                break;
            case IMAGETYPE_WEBP:
                imagewebp($imageResource, $filePath, $quality);
                break;
            default:
                throw new \Exception('Unsupported image type for saving');
        }
    }

    /**
     * Create thumbnail
     */
    protected function createThumbnail($sourceImage, string $directory, string $filename, array $options, int $imageType): void
    {
        $thumbnailPath = $directory . '/thumbnails/' . $filename;

        // Calculate thumbnail dimensions
        $sourceWidth = imagesx($sourceImage);
        $sourceHeight = imagesy($sourceImage);

        [$thumbWidth, $thumbHeight] = $this->calculateDimensions(
            $sourceWidth,
            $sourceHeight,
            $options['thumbnail_width'],
            $options['thumbnail_height']
        );

        // Create thumbnail
        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);

        // Preserve transparency
        if ($imageType === IMAGETYPE_PNG || $imageType === IMAGETYPE_GIF) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }

        // Resize
        imagecopyresampled(
            $thumbnail, $sourceImage,
            0, 0, 0, 0,
            $thumbWidth, $thumbHeight,
            $sourceWidth, $sourceHeight
        );

        // Save thumbnail
        $tempPath = tempnam(sys_get_temp_dir(), 'thumb_');
        $this->saveImageToFile($thumbnail, $tempPath, $imageType, 85);

        Storage::disk('public')->put($thumbnailPath, file_get_contents($tempPath));
        unlink($tempPath);

        imagedestroy($thumbnail);
    }

    /**
     * Get thumbnail path
     */
    protected function getThumbnailPath(string $originalPath): string
    {
        $pathInfo = pathinfo($originalPath);
        return $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['basename'];
    }

    /**
     * Get file URL
     */
    public function getFileUrl(string $path): string
    {
        return asset('storage/' . $path);
    }

    /**
     * Get thumbnail URL
     */
    public function getThumbnailUrl(string $originalPath): string
    {
        $thumbnailPath = $this->getThumbnailPath($originalPath);

        if (Storage::disk('public')->exists($thumbnailPath)) {
            return asset('storage/' . $thumbnailPath);
        }

        return $this->getFileUrl($originalPath);
    }

    /**
     * Check if file exists
     */
    public function fileExists(string $path): bool
    {
        return Storage::disk('public')->exists($path);
    }

    /**
     * Get file size
     */
    public function getFileSize(string $path): int
    {
        return Storage::disk('public')->size($path);
    }

    /**
     * Get file info
     */
    public function getFileInfo(string $path): array
    {
        if (!$this->fileExists($path)) {
            throw new \Exception('File not found: ' . $path);
        }

        return [
            'path' => $path,
            'url' => $this->getFileUrl($path),
            'thumbnail_url' => $this->getThumbnailUrl($path),
            'size' => $this->getFileSize($path),
            'exists' => true,
        ];
    }
}
