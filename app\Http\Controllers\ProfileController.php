<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Rules\Password;
use App\Models\User;
use App\Services\FileUploadService;
use App\Services\SimpleFileUploadService;
use Inertia\Inertia;

class ProfileController extends Controller
{
    /**
     * Display the user's profile page
     */
    public function show()
    {
        /** @var User $user */
        $user = Auth::user();

        return Inertia::render('User/Profile', [
            'user' => $user->only([
                'id', 'name', 'email', 'phone', 'location', 'bio', 
                'organization_name', 'organization_type', 'avatar',
                'bank_name', 'account_number', 'account_name',
                'email_verified_at', 'created_at'
            ]),
        ]);
    }

    /**
     * Update the user's profile information
     */
    public function updateProfile(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'location' => 'nullable|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'organization_name' => 'nullable|string|max:255',
            'organization_type' => 'nullable|string|max:100',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            $fileUploadService = extension_loaded('gd') ? new FileUploadService() : new SimpleFileUploadService();
            
            try {
                // Delete old avatar if exists
                if ($user->avatar) {
                    $fileUploadService->deleteFile($user->avatar);
                }

                $validated['avatar'] = $fileUploadService->uploadImage(
                    $request->file('avatar'),
                    'avatars',
                    [
                        'max_width' => 400,
                        'max_height' => 400,
                        'quality' => 90,
                        'create_thumbnail' => true,
                        'thumbnail_width' => 150,
                        'thumbnail_height' => 150,
                    ]
                );
            } catch (\Exception $e) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['avatar' => 'Failed to upload avatar: ' . $e->getMessage()]);
            }
        }

        $user->update($validated);

        return redirect()->back()
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's password
     */
    public function updatePassword(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $validated = $request->validate([
            'current_password' => 'required|string',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        // Verify current password
        if (!Hash::check($validated['current_password'], $user->password)) {
            throw ValidationException::withMessages([
                'current_password' => 'The current password is incorrect.',
            ]);
        }

        $user->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->back()
            ->with('success', 'Password updated successfully!');
    }

    /**
     * Update bank details
     */
    public function updateBankDetails(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $validated = $request->validate([
            'bank_name' => 'required|string|max:255',
            'account_number' => 'required|string|max:20',
            'account_name' => 'required|string|max:255',
        ]);

        $user->update($validated);

        return redirect()->back()
            ->with('success', 'Bank details updated successfully!');
    }

    /**
     * Update notification preferences
     */
    public function updateNotifications(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'event_reminders' => 'boolean',
            'marketing_emails' => 'boolean',
        ]);

        // Store notification preferences in user's preferences field
        $preferences = $user->preferences ?? [];
        $preferences['notifications'] = $validated;
        
        $user->update(['preferences' => $preferences]);

        return redirect()->back()
            ->with('success', 'Notification preferences updated successfully!');
    }

    /**
     * Delete user account
     */
    public function deleteAccount(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $validated = $request->validate([
            'password' => 'required|string',
            'confirmation' => 'required|string|in:DELETE',
        ]);

        // Verify password
        if (!Hash::check($validated['password'], $user->password)) {
            throw ValidationException::withMessages([
                'password' => 'The password is incorrect.',
            ]);
        }

        // Check if user has active events or pending withdrawals
        $activeEvents = $user->events()->where('is_active', true)->where('event_date', '>=', now())->count();
        $pendingWithdrawals = $user->withdrawals()->where('status', 'pending')->count();

        if ($activeEvents > 0) {
            throw ValidationException::withMessages([
                'account' => 'Cannot delete account with active events. Please deactivate or complete all events first.',
            ]);
        }

        if ($pendingWithdrawals > 0) {
            throw ValidationException::withMessages([
                'account' => 'Cannot delete account with pending withdrawals. Please wait for withdrawals to be processed.',
            ]);
        }

        // Delete avatar if exists
        if ($user->avatar) {
            $fileUploadService = extension_loaded('gd') ? new FileUploadService() : new SimpleFileUploadService();
            $fileUploadService->deleteFile($user->avatar);
        }

        // Soft delete or anonymize user data
        $user->update([
            'name' => 'Deleted User',
            'email' => 'deleted_' . $user->id . '@tickget.com',
            'phone' => null,
            'location' => null,
            'bio' => null,
            'organization_name' => null,
            'organization_type' => null,
            'avatar' => null,
            'bank_name' => null,
            'account_number' => null,
            'account_name' => null,
            'is_active' => false,
        ]);

        Auth::logout();

        return redirect()->route('home')
            ->with('success', 'Your account has been deleted successfully.');
    }

    /**
     * Upload avatar
     */
    public function uploadAvatar(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $validated = $request->validate([
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $fileUploadService = extension_loaded('gd') ? new FileUploadService() : new SimpleFileUploadService();

        try {
            // Delete old avatar if exists
            if ($user->avatar) {
                $fileUploadService->deleteFile($user->avatar);
            }

            $avatarPath = $fileUploadService->uploadImage(
                $request->file('avatar'),
                'avatars',
                [
                    'max_width' => 400,
                    'max_height' => 400,
                    'quality' => 90,
                    'create_thumbnail' => true,
                    'thumbnail_width' => 150,
                    'thumbnail_height' => 150,
                ]
            );

            $user->update(['avatar' => $avatarPath]);

            return response()->json([
                'success' => true,
                'message' => 'Avatar uploaded successfully!',
                'avatar_url' => $fileUploadService->getFileUrl($avatarPath),
                'thumbnail_url' => $fileUploadService->getThumbnailUrl($avatarPath),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload avatar: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Remove avatar
     */
    public function removeAvatar()
    {
        /** @var User $user */
        $user = Auth::user();

        if ($user->avatar) {
            $fileUploadService = extension_loaded('gd') ? new FileUploadService() : new SimpleFileUploadService();
            $fileUploadService->deleteFile($user->avatar);

            $user->update(['avatar' => null]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Avatar removed successfully!',
        ]);
    }
}
