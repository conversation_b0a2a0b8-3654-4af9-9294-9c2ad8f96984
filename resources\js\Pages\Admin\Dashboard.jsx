import React from 'react';
import { Head } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  UsersIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import AdminLayout from '../../Layouts/AdminLayout';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const AdminDashboard = ({ admin, stats, recentActivity, chartData }) => {
  // Format currency
  const formatCurrency = (amount) => {
    return `₦${Number(amount).toLocaleString()}`;
  };

  // Real data from backend
  const dashboardStats = [
    {
      name: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      change: `+${stats.totalUsers}`,
      changeType: 'increase',
      icon: UsersIcon,
    },
    {
      name: 'Platform Revenue',
      value: formatCurrency(stats.platformCommission),
      change: `${stats.totalOrders} orders`,
      changeType: 'increase',
      icon: CurrencyDollarIcon,
    },
    {
      name: 'Active Events',
      value: stats.activeEvents.toLocaleString(),
      change: `${stats.totalEvents} total`,
      changeType: 'increase',
      icon: CalendarIcon,
    },
    {
      name: 'Pending Withdrawals',
      value: stats.pendingWithdrawals.toString(),
      change: 'Needs review',
      changeType: stats.pendingWithdrawals > 0 ? 'warning' : 'increase',
      icon: ExclamationTriangleIcon,
    },
  ];

  // Process recent activity data
  const recentActivities = [
    ...recentActivity.users.map(user => ({
      id: `user-${user.id}`,
      type: 'user_registration',
      message: `New user registered: ${user.email}`,
      time: new Date(user.created_at).toLocaleDateString(),
      status: 'success'
    })),
    ...recentActivity.events.map(event => ({
      id: `event-${event.id}`,
      type: 'event_created',
      message: `New event created: "${event.title}"`,
      time: new Date(event.created_at).toLocaleDateString(),
      status: 'info'
    })),
    ...recentActivity.orders.map(order => ({
      id: `order-${order.id}`,
      type: 'order_completed',
      message: `Order completed: ${formatCurrency(order.total_amount)} for "${order.event.title}"`,
      time: new Date(order.created_at).toLocaleDateString(),
      status: 'success'
    }))
  ].slice(0, 5); // Show only 5 most recent

  // Chart data using real backend data
  const userGrowthData = {
    labels: chartData.userGrowth.map(item => `${item.month}/${item.year}`),
    datasets: [
      {
        label: 'New Users',
        data: chartData.userGrowth.map(item => item.users),
        borderColor: 'rgb(79, 70, 229)',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        tension: 0.4,
      },
    ],
  };

  const revenueData = {
    labels: chartData.monthlyRevenue.map(item => `${item.month}/${item.year}`),
    datasets: [
      {
        label: 'Platform Revenue (₦)',
        data: chartData.monthlyRevenue.map(item => item.commission),
        backgroundColor: 'rgba(79, 70, 229, 0.8)',
      },
    ],
  };

  // Event categories data (we'll use a simple breakdown for now)
  const eventCategoriesData = {
    labels: ['Active Events', 'Completed Events', 'Inactive Events'],
    datasets: [
      {
        data: [stats.activeEvents, stats.totalEvents - stats.activeEvents, 0],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(79, 70, 229, 0.8)',
          'rgba(156, 163, 175, 0.8)',
        ],
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
    },
  };

  const getActivityIcon = (type, status) => {
    if (status === 'warning') {
      return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
    }
    return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
  };

  return (
    <AdminLayout admin={admin}>
      <Head title="Admin Dashboard - TickGet" />
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">Monitor platform performance and user activity</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {dashboardStats.map((stat, index) => (
            <motion.div
              key={stat.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden"
            >
              <dt>
                <div className="absolute bg-indigo-500 rounded-md p-3">
                  <stat.icon className="h-6 w-6 text-white" aria-hidden="true" />
                </div>
                <p className="ml-16 text-sm font-medium text-gray-500 truncate">{stat.name}</p>
              </dt>
              <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                <p
                  className={`ml-2 flex items-baseline text-sm font-semibold ${
                    stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {stat.change}
                </p>
              </dd>
            </motion.div>
          ))}
        </div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white p-6 rounded-lg shadow"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
            <Line data={userGrowthData} options={chartOptions} />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-white p-6 rounded-lg shadow"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Revenue</h3>
            <Bar data={revenueData} options={chartOptions} />
          </motion.div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-white p-6 rounded-lg shadow"
          >
            <h3 className="text-lg font-medium text-gray-900 mb-4">Event Categories</h3>
            <Doughnut data={eventCategoriesData} options={chartOptions} />
          </motion.div>

          {/* Recent Activities */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="lg:col-span-2 bg-white shadow rounded-lg"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Activities</h3>
            </div>
            <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="px-6 py-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {getActivityIcon(activity.type, activity.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">{activity.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-white shadow rounded-lg p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 transition-colors text-left"
            >
              <UsersIcon className="h-8 w-8 text-indigo-600 mb-2" />
              <h4 className="font-medium text-gray-900">Manage Users</h4>
              <p className="text-sm text-gray-500">View and manage user accounts</p>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 transition-colors text-left"
            >
              <CalendarIcon className="h-8 w-8 text-indigo-600 mb-2" />
              <h4 className="font-medium text-gray-900">Review Events</h4>
              <p className="text-sm text-gray-500">Monitor and approve events</p>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 transition-colors text-left"
            >
              <CurrencyDollarIcon className="h-8 w-8 text-indigo-600 mb-2" />
              <h4 className="font-medium text-gray-900">Financial Reports</h4>
              <p className="text-sm text-gray-500">View revenue and transactions</p>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="p-4 border border-gray-200 rounded-lg hover:border-indigo-300 transition-colors text-left"
            >
              <ChartBarIcon className="h-8 w-8 text-indigo-600 mb-2" />
              <h4 className="font-medium text-gray-900">Analytics</h4>
              <p className="text-sm text-gray-500">Detailed platform analytics</p>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
