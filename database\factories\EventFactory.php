<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Event;
use App\Models\User;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(3);
        
        return [
            'organizer_id' => User::factory(),
            'title' => $title,
            'slug' => Str::slug($title) . '-' . Str::random(6),
            'description' => $this->faker->paragraphs(3, true),
            'short_description' => $this->faker->sentence(),
            'event_date' => $this->faker->dateTimeBetween('+1 week', '+3 months')->format('Y-m-d'),
            'event_time' => $this->faker->time('H:i'),
            'end_date' => null,
            'end_time' => null,
            'venue' => $this->faker->company . ' Hall',
            'address' => $this->faker->address,
            'city' => $this->faker->randomElement(['Lagos', 'Abuja', 'Port Harcourt', 'Kano', 'Ibadan']),
            'state' => $this->faker->randomElement(['Lagos', 'FCT', 'Rivers', 'Kano', 'Oyo']),
            'country' => 'Nigeria',
            'event_type' => 'public',
            'category' => $this->faker->randomElement(['Conference', 'Workshop', 'Concert', 'Festival', 'Networking', 'Sports']),
            'banner_image' => null,
            'gallery_images' => null,
            'terms_conditions' => $this->faker->paragraph(),
            'additional_info' => $this->faker->paragraph(),
            'contact_email' => $this->faker->email,
            'contact_phone' => $this->faker->phoneNumber,
            'website_url' => $this->faker->url,
            'social_links' => [
                'twitter' => '@' . $this->faker->userName,
                'facebook' => $this->faker->url,
                'instagram' => '@' . $this->faker->userName,
            ],
            'is_active' => true,
            'is_featured' => $this->faker->boolean(20), // 20% chance of being featured
            'max_attendees' => $this->faker->randomElement([null, 50, 100, 200, 500, 1000]),
            'current_attendees' => 0,
            'requires_approval' => false,
            'custom_fields' => null,
            'page_customization' => null,
            'seo_title' => $title,
            'seo_description' => $this->faker->sentence(),
            'view_count' => $this->faker->numberBetween(0, 1000),
        ];
    }

    /**
     * Indicate that the event is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the event is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the event is in the past.
     */
    public function past(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_date' => $this->faker->dateTimeBetween('-3 months', '-1 week')->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the event has limited capacity.
     */
    public function withCapacity(int $capacity): static
    {
        return $this->state(fn (array $attributes) => [
            'max_attendees' => $capacity,
        ]);
    }

    /**
     * Indicate that the event is sold out.
     */
    public function soldOut(): static
    {
        return $this->state(fn (array $attributes) => [
            'max_attendees' => 100,
            'current_attendees' => 100,
        ]);
    }
}
