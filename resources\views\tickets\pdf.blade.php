<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket - {{ $order->event->title }}</title>
    <style>
        @page {
            margin: 20mm;
            size: A4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #ffffff;
        }
        
        .ticket-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            border: 2px solid #e5e7eb;
        }

        .ticket-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            position: relative;
            text-align: center;
        }
        
        .banner-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 2;
        }
        
        .event-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .order-number {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .ticket-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .total-amount {
            font-size: 24px;
            font-weight: bold;
        }
        
        .ticket-body {
            padding: 30px;
        }
        
        .event-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .detail-item {
            margin-bottom: 15px;
        }
        
        .detail-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .tickets-section {
            border-top: 2px solid #f0f0f0;
            padding-top: 30px;
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .ticket-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        
        .ticket-item:last-child {
            border-bottom: none;
        }
        
        .ticket-name {
            font-weight: 600;
            font-size: 16px;
        }
        
        .ticket-quantity {
            color: #666;
            font-size: 14px;
        }
        
        .ticket-price {
            font-weight: 600;
            font-size: 16px;
        }
        
        .qr-section {
            text-align: center;
            border-top: 2px solid #f0f0f0;
            padding-top: 30px;
        }
        
        .qr-code {
            width: 150px;
            height: 150px;
            margin: 0 auto 15px;
            border: 2px solid #eee;
            border-radius: 8px;
            padding: 10px;
        }
        
        .qr-instructions {
            font-size: 14px;
            color: #666;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #eee;
        }
        
        .footer-text {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
        }
        
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 60px;
            color: rgba(102, 126, 234, 0.05);
            font-weight: bold;
            z-index: 1;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="ticket-container">
        <!-- Header with Event Banner -->
        <div class="ticket-header">
            @if($order->event->banner_image)
                @php
                    $bannerUrl = $order->event->banner_image;
                    // Ensure the URL is absolute for PDF generation
                    if (!str_starts_with($bannerUrl, 'http')) {
                        $bannerUrl = public_path('storage/' . ltrim($bannerUrl, '/'));
                    }
                @endphp
                <img src="{{ $bannerUrl }}" alt="{{ $order->event->title }}" class="banner-image">
            @endif
            
            <div class="header-content">
                <h1 class="event-title">{{ $order->event->title }}</h1>
                <p class="order-number">Order #{{ $order->order_number }}</p>
                
                <div class="ticket-info">
                    <div>
                        <div class="detail-label">Status</div>
                        <div class="detail-value" style="color: #10b981;">✓ Confirmed</div>
                    </div>
                    <div>
                        <div class="total-amount">₦{{ number_format($order->total_amount, 2) }}</div>
                        <div style="font-size: 14px; opacity: 0.9;">Total Paid</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Details -->
        <div class="ticket-body">
            <div class="event-details">
                <div>
                    <div class="detail-item">
                        <div class="detail-label">Date & Time</div>
                        <div class="detail-value">
                            {{ \Carbon\Carbon::parse($order->event->event_date)->format('l, F j, Y') }}<br>
                            {{ \Carbon\Carbon::parse($order->event->event_time)->format('g:i A') }}
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Venue</div>
                        <div class="detail-value">
                            {{ $order->event->venue }}<br>
                            {{ $order->event->city }}, {{ $order->event->state }}
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="detail-item">
                        <div class="detail-label">Attendee</div>
                        <div class="detail-value">{{ $order->buyer_name }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Email</div>
                        <div class="detail-value">{{ $order->buyer_email }}</div>
                    </div>
                    
                    @if($order->buyer_phone)
                    <div class="detail-item">
                        <div class="detail-label">Phone</div>
                        <div class="detail-value">{{ $order->buyer_phone }}</div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Tickets Section -->
            <div class="tickets-section">
                <h2 class="section-title">Your Tickets</h2>
                @foreach($order->items as $item)
                <div class="ticket-item">
                    <div>
                        <div class="ticket-name">{{ $item->ticket->name }}</div>
                        <div class="ticket-quantity">Quantity: {{ $item->quantity }}</div>
                    </div>
                    <div class="ticket-price">₦{{ number_format($item->total_price, 2) }}</div>
                </div>
                @endforeach
            </div>

            <!-- Entry Code Section -->
            <div class="qr-section">
                <h3 class="section-title">🎫 Entry Verification</h3>

                <div style="background: white; border: 3px solid #667eea; border-radius: 12px; padding: 20px; margin: 20px auto; display: inline-block;">
                    <div style="font-size: 14px; color: #666; margin-bottom: 10px; text-align: center;">Entry Code:</div>
                    <div style="font-family: 'Courier New', monospace; font-size: 24px; font-weight: bold; color: #333; letter-spacing: 2px; text-align: center;">
                        {{ $order->qr_code ?: $order->order_number }}
                    </div>
                </div>

                <p class="qr-instructions">
                    📱 Present this entry code at the event entrance for quick check-in.<br>
                    ⏰ Please arrive at least 30 minutes before the event starts.<br>
                    💾 Keep this ticket safe - it's your proof of purchase.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p class="footer-text">
                This ticket is valid only for the specified event date and time. 
                No refunds or exchanges allowed unless event is cancelled.
            </p>
            <div class="logo">TickGet</div>
            <p class="footer-text">Powered by TickGet - Your Premier Event Ticketing Platform</p>
        </div>

        <!-- Watermark -->
        <div class="watermark">TICKGET</div>
    </div>
</body>
</html>
