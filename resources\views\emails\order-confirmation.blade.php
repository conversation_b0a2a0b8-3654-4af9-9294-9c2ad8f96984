<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - {{ $order->event->title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-top: 20px solid #38a169;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }
        
        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .success-message {
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
            border: 2px solid #9ae6b4;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .success-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .success-title {
            font-size: 20px;
            font-weight: 700;
            color: #22543d;
            margin-bottom: 10px;
        }
        
        .success-text {
            color: #2f855a;
            font-size: 16px;
        }
        
        .event-banner {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            border-radius: 12px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            background-image: url('{{ $order->event->banner_image ?? "" }}');
            background-size: cover;
            background-position: center;
            position: relative;
        }
        
        .event-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(72, 187, 120, 0.8);
            border-radius: 12px;
        }
        
        .event-banner-content {
            position: relative;
            z-index: 1;
        }
        
        .order-summary {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .summary-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .summary-title::before {
            content: '📋';
            margin-right: 10px;
            font-size: 20px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .summary-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #48bb78;
        }
        
        .summary-label {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .summary-value {
            font-size: 16px;
            color: #2d3748;
            font-weight: 600;
        }
        
        .ticket-list {
            list-style: none;
            padding: 0;
        }
        
        .ticket-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #48bb78;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .ticket-name {
            font-weight: 600;
            color: #2d3748;
        }
        
        .ticket-price {
            font-weight: 700;
            color: #22543d;
        }
        
        .total-section {
            background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
            border: 2px solid #9ae6b4;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        
        .total-label {
            font-size: 14px;
            color: #2f855a;
            margin-bottom: 5px;
        }
        
        .total-amount {
            font-size: 28px;
            font-weight: 800;
            color: #22543d;
        }
        
        .next-steps {
            background-color: #fef5e7;
            border-left: 4px solid #f6ad55;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .next-steps-title {
            font-weight: 600;
            color: #c05621;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            font-size: 16px;
        }
        
        .next-steps-title::before {
            content: '📝';
            margin-right: 8px;
            font-size: 18px;
        }
        
        .steps-list {
            list-style: none;
            padding: 0;
        }
        
        .steps-list li {
            color: #744210;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .steps-list li::before {
            content: '•';
            color: #f6ad55;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            text-align: center;
            margin: 20px auto;
            display: block;
            max-width: 250px;
            transition: transform 0.2s;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(72, 187, 120, 0.4);
        }
        
        .footer {
            background-color: #f7fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            font-size: 14px;
            color: #718096;
            margin-bottom: 15px;
        }
        
        .footer-links {
            font-size: 12px;
            color: #a0aec0;
        }
        
        .footer-links a {
            color: #48bb78;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .header, .content, .footer {
                padding: 30px 20px;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
            
            .event-banner {
                height: 150px;
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="images/Tickgetlogo.png" alt="TickGet Logo">
            </div>
            <h1>TickGet</h1>
            <p>Your Trusted Event Ticketing Platform</p>
        </div>
        
        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Hello {{ $order->buyer_name }}! 👋
            </div>
            
            <!-- Success Message -->
            <div class="success-message">
                <div class="success-icon">✅</div>
                <div class="success-title">Order Confirmed!</div>
                <div class="success-text">
                    Thank you for your purchase. Your order has been successfully confirmed.
                </div>
            </div>
            
            <!-- Event Banner -->
            <div class="event-banner">
                <div class="event-banner-content">
                    {{ $order->event->title }}
                </div>
            </div>

            <!-- Order Summary -->
            <div class="order-summary">
                <div class="summary-title">Order Summary</div>

                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-label">Order Number</div>
                        <div class="summary-value">{{ $order->order_number }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Event Date</div>
                        <div class="summary-value">{{ $order->event->event_date->format('M j, Y') }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Event Time</div>
                        <div class="summary-value">{{ $order->event->event_time }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Venue</div>
                        <div class="summary-value">{{ $order->event->venue }}</div>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <div class="summary-label">Your Tickets</div>
                    <ul class="ticket-list">
                        @foreach($order->items as $item)
                        <li class="ticket-item">
                            <span class="ticket-name">{{ $item->ticket->name }} × {{ $item->quantity }}</span>
                            <span class="ticket-price">₦{{ number_format($item->total_price, 2) }}</span>
                        </li>
                        @endforeach
                    </ul>
                </div>

                <div class="total-section">
                    <div class="total-label">Total Amount Paid</div>
                    <div class="total-amount">₦{{ number_format($order->total_amount, 2) }}</div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="next-steps">
                <div class="next-steps-title">What's Next?</div>
                <ul class="steps-list">
                    <li>You will receive your digital tickets via email shortly</li>
                    <li>Save this confirmation email for your records</li>
                    <li>Arrive at the venue 30 minutes before the event starts</li>
                    <li>Present your digital ticket at the entrance</li>
                    <li>Contact the event organizer for any event-specific questions</li>
                </ul>
            </div>

            <!-- Action Button -->
            <a href="{{ route('orders.show', $order->order_number) }}" class="action-button">
                View Your Tickets
            </a>

            <div style="text-align: center; margin-top: 30px; color: #718096;">
                <p>Thank you for choosing TickGet! We hope you enjoy the event.</p>
                <p style="margin-top: 10px; font-size: 14px;">
                    If you have any questions, please contact our support team.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">
                This email was sent from TickGet. Please save this confirmation for your records.
            </div>
            <div class="footer-links">
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
                <a href="#">Contact Support</a>
            </div>
        </div>
    </div>
</body>
</html>
