<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'ticket_id',
        'quantity',
        'unit_price',
        'total_price',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * Get the order this item belongs to
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the ticket this item is for
     */
    public function ticket()
    {
        return $this->belongsTo(Ticket::class);
    }

    /**
     * Calculate total price
     */
    public function calculateTotal(): float
    {
        return $this->unit_price * $this->quantity;
    }

    /**
     * Get formatted total price
     */
    public function getFormattedTotal(): string
    {
        return '₦' . number_format($this->total_price, 2);
    }
}
