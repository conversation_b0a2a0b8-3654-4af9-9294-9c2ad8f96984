<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'name',
        'description',
        'price',
        'quantity_available',
        'quantity_sold',
        'min_purchase',
        'max_purchase',
        'sale_start_date',
        'sale_end_date',
        'is_active',
        'ticket_type',
        'benefits',
        'restrictions',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'quantity_available' => 'integer',
        'quantity_sold' => 'integer',
        'min_purchase' => 'integer',
        'max_purchase' => 'integer',
        'sale_start_date' => 'datetime',
        'sale_end_date' => 'datetime',
        'is_active' => 'boolean',
        'benefits' => 'array',
        'restrictions' => 'array',
        'sort_order' => 'integer',
    ];

    /**
     * Get the event this ticket belongs to
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get order items for this ticket
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Check if ticket is available for purchase
     */
    public function isAvailable(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->sale_start_date && $this->sale_start_date > now()) {
            return false;
        }

        if ($this->sale_end_date && $this->sale_end_date < now()) {
            return false;
        }

        return $this->getAvailableQuantity() > 0 || $this->quantity_available === null;
    }

    /**
     * Get available quantity
     */
    public function getAvailableQuantity(): int
    {
        // If quantity_available is null, it means unlimited tickets
        if ($this->quantity_available === null) {
            return 999999; // Return a large number for unlimited
        }

        return max(0, $this->quantity_available - $this->quantity_sold);
    }

    /**
     * Check if ticket is sold out
     */
    public function isSoldOut(): bool
    {
        // If quantity_available is null, it's never sold out (unlimited)
        if ($this->quantity_available === null) {
            return false;
        }

        return $this->getAvailableQuantity() <= 0;
    }

    /**
     * Reserve tickets (increment sold quantity)
     */
    public function reserve(int $quantity): bool
    {
        // If unlimited tickets (quantity_available is null), always allow reservation
        if ($this->quantity_available === null) {
            $this->increment('quantity_sold', $quantity);
            return true;
        }

        if ($this->getAvailableQuantity() < $quantity) {
            return false;
        }

        $this->increment('quantity_sold', $quantity);
        return true;
    }

    /**
     * Release reserved tickets (decrement sold quantity)
     */
    public function release(int $quantity): void
    {
        $this->decrement('quantity_sold', $quantity);
    }

    /**
     * Get price with currency formatting
     */
    public function getFormattedPrice(): string
    {
        return '₦' . number_format($this->price, 2);
    }

    /**
     * Scope for active tickets
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for available tickets
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('sale_start_date')
                  ->orWhere('sale_start_date', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('sale_end_date')
                  ->orWhere('sale_end_date', '>=', now());
            })
            ->whereRaw('quantity_available > quantity_sold');
    }
}
