<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Ticket;
use App\Models\Event;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Ticket>
 */
class TicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'event_id' => Event::factory(),
            'name' => $this->faker->randomElement(['General Admission', 'VIP', 'Early Bird', 'Student', 'Regular']),
            'description' => $this->faker->sentence(),
            'price' => $this->faker->randomElement([0, 1000, 2500, 5000, 10000, 15000, 25000]),
            'quantity_available' => $this->faker->numberBetween(10, 500),
            'quantity_sold' => 0,
            'min_purchase' => 1,
            'max_purchase' => $this->faker->numberBetween(5, 10),
            'sale_start_date' => now()->subDays(7),
            'sale_end_date' => $this->faker->dateTimeBetween('+1 day', '+2 months'),
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(1, 10),
            'benefits' => [
                $this->faker->sentence(),
                $this->faker->sentence(),
            ],
        ];
    }

    /**
     * Indicate that the ticket is free.
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => 0,
        ]);
    }

    /**
     * Indicate that the ticket is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the ticket is sold out.
     */
    public function soldOut(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity_available' => 50,
            'quantity_sold' => 50,
        ]);
    }

    /**
     * Set a specific price for the ticket.
     */
    public function withPrice(int $price): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $price,
        ]);
    }

    /**
     * Set a specific quantity for the ticket.
     */
    public function withQuantity(int $quantity): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity_available' => $quantity,
        ]);
    }
}
