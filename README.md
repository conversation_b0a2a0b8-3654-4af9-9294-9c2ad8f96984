# TickGet - Event Ticketing Platform

TickGet is a comprehensive event ticketing platform built with Laravel 11 and React (Inertia.js). It allows event organizers to create and manage events, sell tickets, and handle payments through Paystack integration with a 10% platform commission.

## Features

### 🎫 Core Features
- **Event Management**: Create, edit, and manage events with rich descriptions and media
- **Ticket Sales**: Multiple ticket types with different pricing and availability
- **Payment Processing**: Secure payments via Paystack with 10% platform commission
- **QR Code Tickets**: Generate QR codes for ticket validation
- **User Dashboard**: Analytics, revenue tracking, and event management
- **Admin Panel**: User management, event oversight, and withdrawal processing

### 🚀 Advanced Features

- **Public Event Browsing**: Search and filter events by category, location, and date
- **File Upload System**: Optimized image uploads with automatic resizing
- **Email Notifications**: Automated emails for orders, payments, and withdrawals
- **Withdrawal System**: Request and manage payouts with admin approval
- **Profile Management**: Complete user profile and organization settings
- **Responsive Design**: Modern, mobile-friendly interface

## Tech Stack

- **Backend**: Laravel 11, PHP 8.2+
- **Frontend**: React 18, Inertia.js, Tailwind CSS
- **Database**: SQLite (configurable)
- **Payment**: Paystack API
- **Email**: Laravel Mail
- **File Storage**: Local storage with optimization

## Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd TickGet
   composer install
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. **Configure Paystack**
   Add your Paystack keys to `.env`:
   ```env
   PAYSTACK_PUBLIC_KEY=your_public_key
   PAYSTACK_SECRET_KEY=your_secret_key
   ```

4. **Database & Assets**
   ```bash
   php artisan migrate
   php artisan storage:link
   npm run build
   ```

5. **Start Development**
   ```bash
   php artisan serve
   npm run dev
   ```

## Admin Access
- **Email**: <EMAIL>
- **Password**: Set during database seeding

## Key Features Implemented

✅ **Complete Frontend** - All pages, layouts, and components built
✅ **Event Management** - CRUD operations with image uploads
✅ **Ticket System** - Multiple types, QR codes, validation
✅ **Paystack Integration** - Secure payments with webhooks
✅ **User Dashboard** - Real analytics and revenue tracking
✅ **Admin Panel** - User management and withdrawal processing
✅ **Email Notifications** - Automated order and payment emails
✅ **File Uploads** - Optimized image handling
✅ **Profile Management** - Complete user settings
✅ **Public Browsing** - Event search and filtering

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[WebReinvent](https://webreinvent.com/)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Jump24](https://jump24.co.uk)**
- **[Redberry](https://redberry.international/laravel/)**
- **[Active Logic](https://activelogic.com)**
- **[byte5](https://byte5.de)**
- **[OP.GG](https://op.gg)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
