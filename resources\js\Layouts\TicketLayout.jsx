import React from 'react';
import { motion } from 'framer-motion';
import { Link } from '@inertiajs/react';

const TicketLayout = ({ children, event }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <img className="h-8 w-auto" src="/images/Tickgetlogo.png" alt="TickGet" />
              <span className="ml-2 text-sm text-gray-500">Powered by TickGet</span>
            </div>
            <div className="text-right">
              <h1 className="text-lg font-semibold text-gray-900">{event?.title || 'Event'}</h1>
              <p className="text-sm text-gray-500">{event?.organizer?.name || 'Organizer'}</p>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <motion.main
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex-1"
      >
        {children}
      </motion.main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p className="text-sm text-gray-500">
              Secure ticket purchasing powered by{' '}
              <Link href="/" className="text-indigo-600 hover:text-indigo-500">
                TickGet
              </Link>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TicketLayout;
