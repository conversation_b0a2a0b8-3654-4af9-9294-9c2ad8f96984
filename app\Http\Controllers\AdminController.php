<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use App\Models\User;
use App\Models\Event;
use App\Models\Order;
use App\Models\Withdrawal;
use App\Notifications\EventStatusNotification;
use Inertia\Inertia;

class AdminController extends Controller
{
    /**
     * Display admin dashboard with analytics
     */
    public function dashboard()
    {
        // Platform statistics
        $totalUsers = User::where('role', '!=', 'admin')->count();
        $totalOrganizers = User::where('role', 'organization')->count();
        $totalEvents = Event::count();
        $activeEvents = Event::where('is_active', true)->where('event_date', '>=', now())->count();
        $totalOrders = Order::where('status', 'completed')->count();
        $totalRevenue = Order::where('status', 'completed')->sum('total_amount');
        $platformCommission = Order::where('status', 'completed')->sum('platform_fee');
        $pendingWithdrawals = Withdrawal::where('status', 'pending')->count();

        // Recent activity
        $recentUsers = User::where('role', '!=', 'admin')
            ->latest()
            ->take(5)
            ->get();

        $recentEvents = Event::with('organizer')
            ->latest()
            ->take(5)
            ->get();

        $recentOrders = Order::with(['event', 'user'])
            ->where('status', 'completed')
            ->latest()
            ->take(5)
            ->get();

        // Monthly revenue chart data (SQLite compatible)
        $monthlyRevenue = Order::where('status', 'completed')
            ->where('created_at', '>=', now()->subMonths(12))
            ->selectRaw("strftime('%m', created_at) as month, strftime('%Y', created_at) as year, SUM(total_amount) as revenue, SUM(platform_fee) as commission")
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        // User growth chart data (SQLite compatible)
        $userGrowth = User::where('role', '!=', 'admin')
            ->where('created_at', '>=', now()->subMonths(12))
            ->selectRaw("strftime('%m', created_at) as month, strftime('%Y', created_at) as year, COUNT(*) as users")
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return Inertia::render('Admin/Dashboard', [
            'admin' => Auth::user(),
            'stats' => [
                'totalUsers' => $totalUsers,
                'totalOrganizers' => $totalOrganizers,
                'totalEvents' => $totalEvents,
                'activeEvents' => $activeEvents,
                'totalOrders' => $totalOrders,
                'totalRevenue' => $totalRevenue,
                'platformCommission' => $platformCommission,
                'pendingWithdrawals' => $pendingWithdrawals,
            ],
            'recentActivity' => [
                'users' => $recentUsers,
                'events' => $recentEvents,
                'orders' => $recentOrders,
            ],
            'chartData' => [
                'monthlyRevenue' => $monthlyRevenue,
                'userGrowth' => $userGrowth,
            ]
        ]);
    }

    /**
     * Display users management page
     */
    public function users(Request $request)
    {
        $query = User::where('role', '!=', 'admin')
            ->withCount(['events', 'orders'])
            ->with(['events' => function($q) {
                $q->withSum('orders as total_revenue', 'total_amount');
            }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('organization_name', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->latest()
            ->paginate(20)
            ->withQueryString();

        // Calculate total revenue for each user
        $users->getCollection()->transform(function ($user) {
            $user->total_revenue = $user->events->sum(function ($event) {
                return $event->orders()->where('status', 'completed')->sum('total_amount');
            });
            return $user;
        });

        return Inertia::render('Admin/Users', [
            'admin' => Auth::user(),
            'users' => $users,
            'filters' => $request->only(['search', 'role', 'status'])
        ]);
    }

    /**
     * Display events management page
     */
    public function events(Request $request)
    {
        $query = Event::with(['organizer', 'tickets']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('organizer', function ($orgQuery) use ($search) {
                      $orgQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            } elseif ($request->status === 'expired') {
                $query->where('event_date', '<', now());
            }
        }

        $events = $query->withCount(['orders as total_orders', 'orders as completed_orders' => function ($q) {
                $q->where('status', 'completed');
            }])
            ->latest()
            ->paginate(20)
            ->withQueryString();

        // Calculate revenue for each event
        $events->getCollection()->transform(function ($event) {
            $event->revenue = $event->orders()->where('status', 'completed')->sum('total_amount');
            return $event;
        });

        return Inertia::render('Admin/Events', [
            'admin' => Auth::user(),
            'events' => $events,
            'filters' => $request->only(['search', 'status'])
        ]);
    }

    /**
     * Display withdrawals management page
     */
    public function withdrawals(Request $request)
    {
        $query = Withdrawal::with('user');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $withdrawals = $query->latest()
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('Admin/Withdrawals', [
            'admin' => Auth::user(),
            'withdrawals' => $withdrawals,
            'filters' => $request->only(['status'])
        ]);
    }

    /**
     * Approve a withdrawal
     */
    public function approveWithdrawal(Request $request, Withdrawal $withdrawal)
    {
        try {
            Log::info('Approving withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'request_data' => $request->all(),
                'withdrawal_status' => $withdrawal->status
            ]);

            $adminNotes = $request->input('admin_notes', 'Approved by admin');

            $withdrawal->approve($adminNotes);

            Log::info('Withdrawal approved successfully', [
                'withdrawal_id' => $withdrawal->id,
                'new_status' => $withdrawal->fresh()->status
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal approved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error approving withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to approve withdrawal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reject a withdrawal
     */
    public function rejectWithdrawal(Request $request, Withdrawal $withdrawal)
    {
        try {
            $validated = $request->validate([
                'admin_notes' => 'required|string|max:500'
            ]);

            Log::info('Rejecting withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'admin_notes' => $validated['admin_notes']
            ]);

            $withdrawal->reject($validated['admin_notes']);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal rejected successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error rejecting withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to reject withdrawal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete a withdrawal
     */
    public function completeWithdrawal(Request $request, Withdrawal $withdrawal)
    {
        try {
            $adminNotes = $request->input('admin_notes', 'Payment processed successfully');

            Log::info('Completing withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'admin_notes' => $adminNotes
            ]);

            $withdrawal->complete($adminNotes);

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal completed successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error completing withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to complete withdrawal: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle user status
     */
    public function toggleUserStatus(User $user)
    {
        if ($user->isAdmin()) {
            throw ValidationException::withMessages([
                'user' => 'Cannot modify admin user status.'
            ]);
        }

        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "User {$status} successfully"
        ]);
    }

    /**
     * Toggle event status
     */
    public function toggleEventStatus(Event $event)
    {
        $previousStatus = $event->is_active;
        $event->update(['is_active' => !$event->is_active]);

        $status = $event->is_active ? 'activated' : 'deactivated';

        // Send notification to event organizer if status changed
        if ($previousStatus !== $event->is_active) {
            $notificationStatus = $event->is_active ? 'active' : 'inactive';
            $adminNotes = $event->is_active ? 'Your event has been approved and is now live.' : 'Your event has been deactivated by admin.';

            $event->organizer->notify(new EventStatusNotification($event, $notificationStatus, $adminNotes));
        }

        return response()->json([
            'success' => true,
            'message' => "Event {$status} successfully"
        ]);
    }

    /**
     * Display admin settings page
     */
    public function settings()
    {
        $admins = User::where('role', 'admin')->get();

        // Get recent admin activities from withdrawals
        $adminActivities = Withdrawal::with(['user', 'event'])
            ->whereNotNull('admin_notes')
            ->whereIn('status', ['approved', 'completed', 'rejected'])
            ->orderBy('updated_at', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($withdrawal) {
                $action = '';
                $type = '';

                switch ($withdrawal->status) {
                    case 'approved':
                        $action = 'Approved withdrawal';
                        $type = 'approval';
                        break;
                    case 'completed':
                        $action = 'Completed withdrawal';
                        $type = 'completion';
                        break;
                    case 'rejected':
                        $action = 'Rejected withdrawal';
                        $type = 'rejection';
                        break;
                }

                return [
                    'id' => $withdrawal->id,
                    'admin_name' => 'Admin', // You might want to track which admin did the action
                    'action' => $action,
                    'details' => "WD-{$withdrawal->id} for ₦" . number_format($withdrawal->amount, 2),
                    'timestamp' => $withdrawal->updated_at->toISOString(),
                    'type' => $type,
                    'notes' => $withdrawal->admin_notes
                ];
            });

        return Inertia::render('Admin/Settings', [
            'admin' => Auth::user(),
            'admins' => $admins,
            'adminActivities' => $adminActivities
        ]);
    }

    /**
     * Create a new admin user
     */
    public function createAdmin(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role' => 'admin',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        return redirect()->back()
            ->with('success', 'Admin user created successfully.');
    }

    /**
     * Delete an admin user
     */
    public function deleteAdmin(User $admin)
    {
        if (!$admin->isAdmin()) {
            throw ValidationException::withMessages([
                'admin' => 'User is not an admin.'
            ]);
        }

        // Prevent deleting the last admin
        if (User::where('role', 'admin')->count() <= 1) {
            throw ValidationException::withMessages([
                'admin' => 'Cannot delete the last admin user.'
            ]);
        }

        // Prevent self-deletion
        if ($admin->id === Auth::id()) {
            throw ValidationException::withMessages([
                'admin' => 'Cannot delete your own admin account.'
            ]);
        }

        $admin->delete();

        return redirect()->back()
            ->with('success', 'Admin user deleted successfully.');
    }

    /**
     * Display analytics page with comprehensive platform statistics
     */
    public function analytics()
    {
        // Basic platform statistics
        $totalUsers = User::where('role', '!=', 'admin')->count();
        $totalOrganizers = User::where('role', 'organization')->count();
        $totalEvents = Event::count();
        $activeEvents = Event::where('is_active', true)->where('event_date', '>=', now())->count();
        $totalTicketsSold = Order::where('status', 'completed')->count();
        $totalRevenue = Order::where('status', 'completed')->sum('total_amount');
        $platformCommission = Order::where('status', 'completed')->sum('platform_fee');
        $totalViews = Event::sum('views');

        // Monthly revenue comparison
        $thisMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();
        $thisMonthRevenue = Order::where('status', 'completed')
            ->whereBetween('created_at', [$thisMonth, now()])
            ->sum('total_amount');
        $lastMonthRevenue = Order::where('status', 'completed')
            ->whereBetween('created_at', [$lastMonth, $thisMonth])
            ->sum('total_amount');

        // Chart data for last 6 months
        $chartData = [];
        for ($i = 5; $i >= 0; $i--) {
            $monthStart = now()->subMonths($i)->startOfMonth();
            $monthEnd = now()->subMonths($i)->endOfMonth();

            $monthRevenue = Order::where('status', 'completed')
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->sum('total_amount');

            $monthUsers = User::where('role', '!=', 'admin')
                ->whereBetween('created_at', [$monthStart, $monthEnd])
                ->count();

            $monthEvents = Event::whereBetween('created_at', [$monthStart, $monthEnd])
                ->count();

            $chartData[] = [
                'month' => $monthStart->format('M'),
                'revenue' => $monthRevenue,
                'users' => $monthUsers,
                'events' => $monthEvents
            ];
        }

        // Top performing events
        $topEvents = Event::withCount(['orders as completed_orders' => function ($query) {
                $query->where('status', 'completed');
            }])
            ->with('organizer')
            ->get()
            ->map(function ($event) {
                $event->revenue = $event->orders()->where('status', 'completed')->sum('total_amount');
                return $event;
            })
            ->sortByDesc('revenue')
            ->take(5)
            ->values();

        return Inertia::render('Admin/Analytics', [
            'admin' => Auth::user(),
            'stats' => [
                'totalUsers' => $totalUsers,
                'totalOrganizers' => $totalOrganizers,
                'totalEvents' => $totalEvents,
                'activeEvents' => $activeEvents,
                'totalTicketsSold' => $totalTicketsSold,
                'totalRevenue' => $totalRevenue,
                'platformCommission' => $platformCommission,
                'totalViews' => $totalViews,
                'thisMonthRevenue' => $thisMonthRevenue,
                'lastMonthRevenue' => $lastMonthRevenue,
            ],
            'chartData' => $chartData,
            'topEvents' => $topEvents
        ]);
    }
}
