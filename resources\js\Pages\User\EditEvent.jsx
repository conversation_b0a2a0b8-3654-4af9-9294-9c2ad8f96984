import { useState, useEffect } from 'react';
import { Head, useForm, router, usePage } from '@inertiajs/react';
import { motion } from 'framer-motion';
import { XMarkIcon, PlusIcon, MinusIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import AppLayout from '../../Layouts/AppLayout';
import ErrorAlert from '../../Components/Notifications/ErrorAlert';
import SuccessAlert from '../../Components/Notifications/SuccessAlert';

const EditEvent = ({ user = {}, event = {} }) => {
  const { props } = usePage();

  // Format date and time for form inputs
  const formatDateForInput = (date) => {
    if (!date) return '';
    return new Date(date).toISOString().split('T')[0];
  };

  const formatTimeForInput = (time) => {
    if (!time) return '';

    // Handle different time formats
    if (typeof time === 'string') {
      // If already in HH:MM format
      if (time.match(/^\d{2}:\d{2}$/)) {
        return time;
      }
      // If in HH:MM:SS format
      if (time.match(/^\d{2}:\d{2}:\d{2}$/)) {
        return time.substring(0, 5);
      }
      // If includes T (ISO format)
      if (time.includes('T')) {
        return time.split('T')[1].substring(0, 5);
      }
    }

    try {
      // Try to parse as date and extract time
      const dateObj = new Date(`2024-01-01T${time}`);
      if (!isNaN(dateObj.getTime())) {
        return dateObj.toTimeString().substring(0, 5);
      }
    } catch (error) {
      console.error('Error formatting time:', time, error);
    }

    return '';
  };

  // Inertia form for event editing
  const { data, setData, processing, errors } = useForm({
    title: event.title || '',
    description: event.description || '',
    short_description: event.short_description || '',
    event_date: formatDateForInput(event.event_date),
    event_time: formatTimeForInput(event.event_time),
    end_date: formatDateForInput(event.end_date),
    end_time: formatTimeForInput(event.end_time),
    venue: event.venue || '',
    address: event.address || '',
    city: event.city || '',
    state: event.state || '',
    country: event.country || 'Nigeria',
    category: event.category || '',
    banner_image: null,
    gallery_images: [],
    terms_conditions: event.terms_conditions || '',
    additional_info: event.additional_info || '',
    contact_email: event.contact_email || user?.email || '',
    contact_phone: event.contact_phone || user?.phone || '',
    website_url: event.website_url || '',
    social_links: event.social_links || {},
    max_attendees: event.max_attendees || '',
    requires_approval: event.requires_approval || false,
    custom_fields: event.custom_fields || {},
    page_customization: event.page_customization || {},
    seo_title: event.seo_title || '',
    seo_description: event.seo_description || '',
    event_type: event.event_type || 'public',
    is_active: event.is_active !== undefined ? event.is_active : true,
  });

  // Local state for UI
  const [tags, setTags] = useState(event.tags || []);
  const [currentTag, setCurrentTag] = useState('');
  const [bannerPreview, setBannerPreview] = useState(
    event.banner_image ? (event.banner_image.startsWith('http') ? event.banner_image : `/storage/${event.banner_image}`) : null
  );
  const [galleryPreviews, setGalleryPreviews] = useState(
    event.gallery_images ? event.gallery_images.map(img =>
      img.startsWith('http') ? img : `/storage/${img}`
    ) : []
  );
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Social links state
  const [socialLinks, setSocialLinks] = useState(
    event.social_links || {
      facebook: '',
      twitter: '',
      instagram: '',
      linkedin: '',
      youtube: '',
      website: ''
    }
  );

  // Ticket types state - load existing tickets
  const [ticketTypes, setTicketTypes] = useState(
    event.tickets && event.tickets.length > 0
      ? event.tickets.map(ticket => ({
          id: ticket.id,
          name: ticket.name,
          description: ticket.description || '',
          price: ticket.price.toString(),
          quantity: ticket.quantity_available ? ticket.quantity_available.toString() : '',
          is_active: ticket.is_active,
          sort_order: ticket.sort_order || 1
        }))
      : [{
          id: Date.now(),
          name: 'General Admission',
          description: 'Standard entry to the event',
          price: '0',
          quantity: '',
          is_active: true,
          sort_order: 1
        }]
  );

  // Event categories
  const eventCategories = [
    'Conference', 'Workshop', 'Seminar', 'Networking', 'Concert', 'Festival',
    'Sports', 'Exhibition', 'Trade Show', 'Business', 'Technology', 'Education',
    'Health & Wellness', 'Food & Drink', 'Arts & Culture', 'Music', 'Comedy',
    'Fashion', 'Gaming', 'Charity', 'Religious', 'Community', 'Other'
  ];

  // Nigerian states
  const nigerianStates = [
    'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno',
    'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'Gombe', 'Imo',
    'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos',
    'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers',
    'Sokoto', 'Taraba', 'Yobe', 'Zamfara', 'FCT (Abuja)'
  ];

  const [customCategory, setCustomCategory] = useState('');

  // Handle flash messages
  useEffect(() => {
    if (props.flash) {
      // Show success notification
      if (props.flash.success) {
        setSuccessMessage(props.flash.success);
        setShowSuccessAlert(true);
      }
    }
  }, [props.flash]);

  // Ticket type management functions
  const addTicketType = () => {
    const newTicket = {
      id: Date.now(),
      name: '',
      description: '',
      price: '',
      quantity: '',
      is_active: true,
      sort_order: ticketTypes.length + 1
    };
    setTicketTypes([...ticketTypes, newTicket]);
  };

  const removeTicketType = (id) => {
    if (ticketTypes.length > 1) {
      setTicketTypes(ticketTypes.filter(ticket => ticket.id !== id));
    }
  };

  const updateTicketType = (id, field, value) => {
    setTicketTypes(ticketTypes.map(ticket =>
      ticket.id === id ? { ...ticket, [field]: value } : ticket
    ));
  };

  // Handle tags
  const handleTagInput = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const tag = currentTag.trim();
      if (tag && !tags.includes(tag)) {
        setTags([...tags, tag]);
        setCurrentTag('');
      }
    }
  };

  const removeTag = (tagToRemove) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Handle category selection
  const handleCategoryChange = (e) => {
    const value = e?.target?.value || '';
    if (value === 'custom') {
      setData('category', customCategory || '');
    } else {
      setData('category', value);
      setCustomCategory('');
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    const formData = new FormData();
    
    // Add all form fields
    Object.keys(data).forEach(key => {
      if (key === 'banner_image' && data[key]) {
        formData.append(key, data[key]);
      } else if (key === 'gallery_images' && data[key].length > 0) {
        data[key].forEach((file, index) => {
          formData.append(`gallery_images[${index}]`, file);
        });
      } else if (typeof data[key] === 'object' && data[key] !== null) {
        formData.append(key, JSON.stringify(data[key]));
      } else if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key]);
      }
    });

    // Add tags if any
    if (tags.length > 0) {
      formData.append('tags', JSON.stringify(tags));
    }

    // Add social links
    const filteredSocialLinks = Object.fromEntries(
      Object.entries(socialLinks).filter(([, value]) => value.trim() !== '')
    );
    if (Object.keys(filteredSocialLinks).length > 0) {
      formData.append('social_links', JSON.stringify(filteredSocialLinks));
    } else {
      // Send empty object to satisfy array validation
      formData.append('social_links', JSON.stringify({}));
    }

    // Add ticket types
    const validTicketTypes = ticketTypes.filter(ticket =>
      ticket.name.trim() !== '' && ticket.price !== ''
    );

    if (validTicketTypes.length > 0) {
      formData.append('ticket_types', JSON.stringify(validTicketTypes));
      console.log('Valid ticket types:', validTicketTypes);
    }

    // Add method override for PUT request
    formData.append('_method', 'PUT');

    router.post(`/events/${event.slug}`, formData, {
      forceFormData: true,
      onSuccess: () => {
        router.visit('/events', {
          onSuccess: () => {
            // Success handled by redirect
          }
        });
      },
      onError: (errors) => {
        console.error('Form submission errors:', errors);

        // Show specific error messages
        let errorMsg = 'There was an error updating the event. Please check the fields and try again.';
        if (typeof errors === 'object') {
          const firstError = Object.values(errors)[0];
          if (Array.isArray(firstError)) {
            errorMsg = firstError[0];
          } else if (typeof firstError === 'string') {
            errorMsg = firstError;
          }
        } else if (typeof errors === 'string') {
          errorMsg = errors;
        }
        
        setErrorMessage(errorMsg);
        setShowErrorAlert(true);
      }
    });
  };

  // Handle banner image upload
  const handleBannerUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setData('banner_image', file);
      const reader = new FileReader();
      reader.onload = (e) => setBannerPreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  // Handle gallery images upload
  const handleGalleryUpload = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setData('gallery_images', [...data.gallery_images, ...files]);
      
      files.forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          setGalleryPreviews(prev => [...prev, e.target.result]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  // Remove gallery image
  const removeGalleryImage = (index) => {
    const newGalleryImages = data.gallery_images.filter((_, i) => i !== index);
    const newPreviews = galleryPreviews.filter((_, i) => i !== index);
    setData('gallery_images', newGalleryImages);
    setGalleryPreviews(newPreviews);
  };



  return (
    <AppLayout user={user}>
      <Head title={`Edit Event: ${event.title} - TickGet`} />
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Edit Event</h1>
          <p className="mt-2 text-gray-600">Update your event details and settings</p>
        </div>

        {/* Event Status Toggle */}
        <div className="bg-white shadow rounded-lg p-6 mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Event Status</h2>
              <p className="mt-1 text-sm text-gray-600">
                {data.is_active ? 'Your event is currently active and visible to the public' : 'Your event is currently inactive and hidden from the public'}
              </p>
            </div>
            <div className="flex items-center">
              <button
                type="button"
                onClick={() => setData('is_active', !data.is_active)}
                className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                  data.is_active ? 'bg-indigo-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                    data.is_active ? 'translate-x-5' : 'translate-x-0'
                  }`}
                />
              </button>
              <span className="ml-3 flex items-center">
                {data.is_active ? (
                  <EyeIcon className="h-5 w-5 text-green-500 mr-1" />
                ) : (
                  <EyeSlashIcon className="h-5 w-5 text-gray-400 mr-1" />
                )}
                <span className={`text-sm font-medium ${data.is_active ? 'text-green-600' : 'text-gray-500'}`}>
                  {data.is_active ? 'Active' : 'Inactive'}
                </span>
              </span>
            </div>
          </div>
        </div>

        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          onSubmit={handleSubmit}
          className="space-y-8"
        >
          {/* Basic Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Event Title *
                </label>
                <input
                  type="text"
                  id="title"
                  value={data.title}
                  onChange={(e) => setData('title', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter event title"
                  required
                />
                {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
              </div>

              <div>
                <label htmlFor="short_description" className="block text-sm font-medium text-gray-700 mb-2">
                  Short Description
                </label>
                <input
                  type="text"
                  id="short_description"
                  value={data.short_description}
                  onChange={(e) => setData('short_description', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Brief description for event listings"
                  maxLength="500"
                />
                {errors.short_description && <p className="mt-1 text-sm text-red-600">{errors.short_description}</p>}
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Event Description *
                </label>
                <textarea
                  id="description"
                  rows="6"
                  value={data.description}
                  onChange={(e) => setData('description', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Describe your event in detail..."
                  required
                />
                {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
              </div>
            </div>
          </div>

          {/* Date and Time */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Date & Time</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="event_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Event Date *
                </label>
                <input
                  type="date"
                  id="event_date"
                  value={data.event_date}
                  onChange={(e) => setData('event_date', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  required
                />
                {errors.event_date && <p className="mt-1 text-sm text-red-600">{errors.event_date}</p>}
              </div>

              <div>
                <label htmlFor="event_time" className="block text-sm font-medium text-gray-700 mb-2">
                  Start Time *
                </label>
                <input
                  type="time"
                  id="event_time"
                  value={data.event_time}
                  onChange={(e) => setData('event_time', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  required
                />
                {errors.event_time && <p className="mt-1 text-sm text-red-600">{errors.event_time}</p>}
              </div>

              <div>
                <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-2">
                  End Date (Optional)
                </label>
                <input
                  type="date"
                  id="end_date"
                  value={data.end_date}
                  onChange={(e) => setData('end_date', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
                {errors.end_date && <p className="mt-1 text-sm text-red-600">{errors.end_date}</p>}
              </div>

              <div>
                <label htmlFor="end_time" className="block text-sm font-medium text-gray-700 mb-2">
                  End Time (Optional)
                </label>
                <input
                  type="time"
                  id="end_time"
                  value={data.end_time}
                  onChange={(e) => setData('end_time', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
                {errors.end_time && <p className="mt-1 text-sm text-red-600">{errors.end_time}</p>}
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Location</h2>

            <div className="grid grid-cols-1 gap-6">
              <div>
                <label htmlFor="venue" className="block text-sm font-medium text-gray-700 mb-2">
                  Venue Name *
                </label>
                <input
                  type="text"
                  id="venue"
                  value={data.venue}
                  onChange={(e) => setData('venue', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="e.g., Convention Center, Hotel Ballroom"
                  required
                />
                {errors.venue && <p className="mt-1 text-sm text-red-600">{errors.venue}</p>}
              </div>

              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Address *
                </label>
                <textarea
                  id="address"
                  rows="3"
                  value={data.address}
                  onChange={(e) => setData('address', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter the complete address"
                  required
                />
                {errors.address && <p className="mt-1 text-sm text-red-600">{errors.address}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-2">
                    City *
                  </label>
                  <input
                    type="text"
                    id="city"
                    value={data.city}
                    onChange={(e) => setData('city', e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="e.g., Lagos"
                    required
                  />
                  {errors.city && <p className="mt-1 text-sm text-red-600">{errors.city}</p>}
                </div>

                <div>
                  <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-2">
                    State *
                  </label>
                  <select
                    id="state"
                    value={data.state}
                    onChange={(e) => setData('state', e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  >
                    <option value="">Select State</option>
                    {nigerianStates.map(state => (
                      <option key={state} value={state}>{state}</option>
                    ))}
                  </select>
                  {errors.state && <p className="mt-1 text-sm text-red-600">{errors.state}</p>}
                </div>

                <div>
                  <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-2">
                    Country *
                  </label>
                  <input
                    type="text"
                    id="country"
                    value={data.country}
                    onChange={(e) => setData('country', e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    required
                  />
                  {errors.country && <p className="mt-1 text-sm text-red-600">{errors.country}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Media Upload */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Event Media</h2>

            {/* Banner Image */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Event Banner Image
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  {bannerPreview ? (
                    <div className="relative">
                      <img
                        src={bannerPreview}
                        alt="Banner preview"
                        className="mx-auto h-32 w-auto object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setBannerPreview(null);
                          setData('banner_image', null);
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <>
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        stroke="currentColor"
                        fill="none"
                        viewBox="0 0 48 48"
                      >
                        <path
                          d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="banner_image"
                          className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                        >
                          <span>Upload a banner image</span>
                          <input
                            id="banner_image"
                            name="banner_image"
                            type="file"
                            className="sr-only"
                            accept="image/*"
                            onChange={handleBannerUpload}
                          />
                        </label>
                        <p className="pl-1">or drag and drop</p>
                      </div>
                      <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                    </>
                  )}
                </div>
              </div>
              {errors.banner_image && <p className="mt-1 text-sm text-red-600">{errors.banner_image}</p>}
            </div>

            {/* Gallery Images */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Gallery Images (Optional)
              </label>
              <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                <div className="space-y-1 text-center">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="gallery_images"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                    >
                      <span>Upload gallery images</span>
                      <input
                        id="gallery_images"
                        name="gallery_images"
                        type="file"
                        className="sr-only"
                        accept="image/*"
                        multiple
                        onChange={handleGalleryUpload}
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB each</p>
                </div>
              </div>

              {/* Gallery Preview */}
              {galleryPreviews.length > 0 && (
                <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                  {galleryPreviews.map((preview, index) => (
                    <div key={index} className="relative">
                      <img
                        src={preview}
                        alt={`Gallery ${index + 1}`}
                        className="h-24 w-full object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => removeGalleryImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              {errors.gallery_images && <p className="mt-1 text-sm text-red-600">{errors.gallery_images}</p>}
            </div>
          </div>

          {/* Category & Type */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Category & Type</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Event Category *
                </label>
                <select
                  id="category"
                  value={data.category}
                  onChange={handleCategoryChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  required
                >
                  <option value="">Select Category</option>
                  {eventCategories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                  <option value="custom">Custom Category</option>
                </select>
                {errors.category && <p className="mt-1 text-sm text-red-600">{errors.category}</p>}
              </div>

              {data.category === 'custom' && (
                <div>
                  <label htmlFor="custom_category" className="block text-sm font-medium text-gray-700 mb-2">
                    Custom Category
                  </label>
                  <input
                    type="text"
                    id="custom_category"
                    value={customCategory}
                    onChange={(e) => {
                      setCustomCategory(e.target.value);
                      setData('category', e.target.value);
                    }}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter custom category"
                  />
                </div>
              )}

              <div>
                <label htmlFor="event_type" className="block text-sm font-medium text-gray-700 mb-2">
                  Event Type
                </label>
                <select
                  id="event_type"
                  value={data.event_type}
                  onChange={(e) => setData('event_type', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  <option value="public">Public Event</option>
                  <option value="private">Private Event</option>
                </select>
                {errors.event_type && <p className="mt-1 text-sm text-red-600">{errors.event_type}</p>}
              </div>

              <div>
                <label htmlFor="max_attendees" className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Attendees (Optional)
                </label>
                <input
                  type="number"
                  id="max_attendees"
                  value={data.max_attendees}
                  onChange={(e) => setData('max_attendees', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Leave empty for unlimited"
                  min="1"
                />
                {errors.max_attendees && <p className="mt-1 text-sm text-red-600">{errors.max_attendees}</p>}
              </div>
            </div>

            <div className="mt-6">
              <div className="flex items-center">
                <input
                  id="requires_approval"
                  type="checkbox"
                  checked={data.requires_approval}
                  onChange={(e) => setData('requires_approval', e.target.checked)}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="requires_approval" className="ml-2 block text-sm text-gray-900">
                  Require approval for attendees
                </label>
              </div>
              <p className="mt-1 text-sm text-gray-500">
                When enabled, attendees will need approval before their registration is confirmed.
              </p>
            </div>
          </div>

          {/* Ticket Types */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Ticket Types</h2>
              <motion.button
                type="button"
                onClick={addTicketType}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Ticket Type
              </motion.button>
            </div>

            <div className="space-y-6">
              {ticketTypes.map((ticket, index) => (
                <motion.div
                  key={ticket.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                      Ticket Type {index + 1}
                    </h3>
                    {ticketTypes.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeTicketType(ticket.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <MinusIcon className="h-5 w-5" />
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Ticket Name *
                      </label>
                      <input
                        type="text"
                        value={ticket.name}
                        onChange={(e) => updateTicketType(ticket.id, 'name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="e.g., General Admission, VIP"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Price (₦) *
                      </label>
                      <input
                        type="number"
                        value={ticket.price}
                        onChange={(e) => updateTicketType(ticket.id, 'price', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="0"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Quantity Available
                      </label>
                      <input
                        type="number"
                        value={ticket.quantity}
                        onChange={(e) => updateTicketType(ticket.id, 'quantity', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Enter the number of tickets available for this type"
                        min="1"
                      />
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id={`ticket-active-${ticket.id}`}
                        checked={ticket.is_active}
                        onChange={(e) => updateTicketType(ticket.id, 'is_active', e.target.checked)}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`ticket-active-${ticket.id}`} className="ml-2 block text-sm text-gray-900">
                        Active
                      </label>
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description (Optional)
                    </label>
                    <textarea
                      rows="2"
                      value={ticket.description}
                      onChange={(e) => updateTicketType(ticket.id, 'description', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                      placeholder="Describe what's included with this ticket..."
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Additional Information</h2>

            <div className="space-y-6">
              <div>
                <label htmlFor="terms_conditions" className="block text-sm font-medium text-gray-700 mb-2">
                  Terms & Conditions
                </label>
                <textarea
                  id="terms_conditions"
                  rows="4"
                  value={data.terms_conditions}
                  onChange={(e) => setData('terms_conditions', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter terms and conditions for your event..."
                />
                {errors.terms_conditions && <p className="mt-1 text-sm text-red-600">{errors.terms_conditions}</p>}
              </div>

              <div>
                <label htmlFor="additional_info" className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Information
                </label>
                <textarea
                  id="additional_info"
                  rows="4"
                  value={data.additional_info}
                  onChange={(e) => setData('additional_info', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Any additional information attendees should know..."
                />
                {errors.additional_info && <p className="mt-1 text-sm text-red-600">{errors.additional_info}</p>}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Contact Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="contact_email" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Email *
                </label>
                <input
                  type="email"
                  id="contact_email"
                  value={data.contact_email}
                  onChange={(e) => setData('contact_email', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="<EMAIL>"
                  required
                />
                {errors.contact_email && <p className="mt-1 text-sm text-red-600">{errors.contact_email}</p>}
              </div>

              <div>
                <label htmlFor="contact_phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Phone
                </label>
                <input
                  type="tel"
                  id="contact_phone"
                  value={data.contact_phone}
                  onChange={(e) => setData('contact_phone', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="+234 xxx xxx xxxx"
                />
                {errors.contact_phone && <p className="mt-1 text-sm text-red-600">{errors.contact_phone}</p>}
              </div>

              <div>
                <label htmlFor="website_url" className="block text-sm font-medium text-gray-700 mb-2">
                  Website URL
                </label>
                <input
                  type="url"
                  id="website_url"
                  value={data.website_url}
                  onChange={(e) => setData('website_url', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="https://example.com"
                />
                {errors.website_url && <p className="mt-1 text-sm text-red-600">{errors.website_url}</p>}
              </div>
            </div>
          </div>

          {/* Social Media Links */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Social Media Links</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="facebook" className="block text-sm font-medium text-gray-700 mb-2">
                  Facebook
                </label>
                <input
                  type="url"
                  id="facebook"
                  value={socialLinks.facebook}
                  onChange={(e) => setSocialLinks({...socialLinks, facebook: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="https://facebook.com/yourpage"
                />
              </div>

              <div>
                <label htmlFor="twitter" className="block text-sm font-medium text-gray-700 mb-2">
                  Twitter
                </label>
                <input
                  type="url"
                  id="twitter"
                  value={socialLinks.twitter}
                  onChange={(e) => setSocialLinks({...socialLinks, twitter: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="https://twitter.com/yourhandle"
                />
              </div>

              <div>
                <label htmlFor="instagram" className="block text-sm font-medium text-gray-700 mb-2">
                  Instagram
                </label>
                <input
                  type="url"
                  id="instagram"
                  value={socialLinks.instagram}
                  onChange={(e) => setSocialLinks({...socialLinks, instagram: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="https://instagram.com/yourhandle"
                />
              </div>

              <div>
                <label htmlFor="linkedin" className="block text-sm font-medium text-gray-700 mb-2">
                  LinkedIn
                </label>
                <input
                  type="url"
                  id="linkedin"
                  value={socialLinks.linkedin}
                  onChange={(e) => setSocialLinks({...socialLinks, linkedin: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="https://linkedin.com/company/yourcompany"
                />
              </div>
            </div>
          </div>

          {/* SEO Settings */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">SEO Settings</h2>
            <p className="text-sm text-gray-600 mb-6">Optimize your event for search engines</p>

            <div className="space-y-6">
              <div>
                <label htmlFor="seo_title" className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Title
                </label>
                <input
                  type="text"
                  id="seo_title"
                  value={data.seo_title}
                  onChange={(e) => setData('seo_title', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Custom title for search engines (leave empty to use event title)"
                  maxLength="60"
                />
                <div className="mt-1 flex justify-between">
                  <div>
                    {errors.seo_title && <p className="text-sm text-red-600">{errors.seo_title}</p>}
                  </div>
                  <span className="text-xs text-gray-400">{data.seo_title.length}/60</span>
                </div>
              </div>

              <div>
                <label htmlFor="seo_description" className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Description
                </label>
                <textarea
                  id="seo_description"
                  rows="3"
                  value={data.seo_description}
                  onChange={(e) => setData('seo_description', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Brief description for search engine results (leave empty to use short description)"
                  maxLength="160"
                />
                <div className="mt-1 flex justify-between">
                  <div>
                    {errors.seo_description && <p className="text-sm text-red-600">{errors.seo_description}</p>}
                  </div>
                  <span className="text-xs text-gray-400">{data.seo_description.length}/160</span>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <motion.a
              href="/events"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium"
            >
              Cancel
            </motion.a>
            <motion.button
              type="submit"
              disabled={processing}
              whileHover={{ scale: processing ? 1 : 1.02 }}
              whileTap={{ scale: processing ? 1 : 0.98 }}
              className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {processing ? (
                <div className="flex items-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Updating Event...
                </div>
              ) : (
                'Update Event'
              )}
            </motion.button>
          </div>

          {/* Error Alert */}
          <ErrorAlert
            show={showErrorAlert}
            onClose={() => setShowErrorAlert(false)}
            title="Error!"
            message={errorMessage}
          />
        </motion.form>

        {/* Success Alert */}
        <SuccessAlert
          show={showSuccessAlert}
          onClose={() => setShowSuccessAlert(false)}
          title="Success!"
          message={successMessage}
        />

        {/* Error Alert */}
        <ErrorAlert
          show={showErrorAlert}
          onClose={() => setShowErrorAlert(false)}
          title="Error!"
          message={errorMessage}
        />
      </div>
    </AppLayout>
  );
};

export default EditEvent;
