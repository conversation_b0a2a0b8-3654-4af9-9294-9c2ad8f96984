<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\ValidationException;
use App\Models\Event;
use App\Models\Ticket;
use App\Models\Order;
use App\Models\OrderItem;
use App\Services\PaystackService;
use App\Notifications\TicketEmailNotification;
use Inertia\Inertia;
use Barryvdh\DomPDF\Facade\Pdf;

class OrderController extends Controller
{
    /**
     * Display the ticket purchasing page for an event
     */
    public function showEventTickets(Event $event)
    {
        // Increment view count
        $event->incrementViewCount();

        // Load active tickets
        $tickets = $event->activeTickets()
            ->available()
            ->orderBy('sort_order')
            ->get();

        // Check if event is expired or sold out
        if ($event->isExpired()) {
            return Inertia::render('Ticket/EventExpired', [
                'event' => $event
            ]);
        }

        if ($event->isSoldOut()) {
            return Inertia::render('Ticket/EventSoldOut', [
                'event' => $event
            ]);
        }

        return Inertia::render('Ticket/EventTickets', [
            'event' => $event->load('organizer'),
            'tickets' => $tickets
        ]);
    }

    /**
     * Create a new order
     */
    public function store(Request $request, Event $event)
    {
        $validated = $request->validate([
            'buyer_name' => 'required|string|max:255',
            'buyer_email' => 'required|email|max:255',
            'buyer_phone' => 'nullable|string|max:20',
            'tickets' => 'required|array|min:1',
            'tickets.*.ticket_id' => 'required|exists:tickets,id',
            'tickets.*.quantity' => 'required|integer|min:1',
            'custom_form_data' => 'nullable|array',
            'notes' => 'nullable|string|max:1000',
        ]);

        return DB::transaction(function () use ($validated, $event, $request) {
            $subtotal = 0;
            $orderItems = [];

            // Validate and calculate order
            foreach ($validated['tickets'] as $ticketData) {
                $ticket = Ticket::findOrFail($ticketData['ticket_id']);

                // Validate ticket belongs to event
                if ($ticket->event_id !== $event->id) {
                    throw ValidationException::withMessages([
                        'tickets' => 'Invalid ticket selection.'
                    ]);
                }

                // Validate ticket availability
                if (!$ticket->isAvailable()) {
                    throw ValidationException::withMessages([
                        'tickets' => "Ticket '{$ticket->name}' is not available for purchase."
                    ]);
                }

                // Validate quantity limits
                $quantity = $ticketData['quantity'];
                if ($quantity < $ticket->min_purchase) {
                    throw ValidationException::withMessages([
                        'tickets' => "Minimum purchase for '{$ticket->name}' is {$ticket->min_purchase}."
                    ]);
                }

                if ($ticket->max_purchase && $quantity > $ticket->max_purchase) {
                    throw ValidationException::withMessages([
                        'tickets' => "Maximum purchase for '{$ticket->name}' is {$ticket->max_purchase}."
                    ]);
                }

                if ($quantity > $ticket->getAvailableQuantity()) {
                    throw ValidationException::withMessages([
                        'tickets' => "Only {$ticket->getAvailableQuantity()} tickets available for '{$ticket->name}'."
                    ]);
                }

                // Reserve tickets
                if (!$ticket->reserve($quantity)) {
                    throw ValidationException::withMessages([
                        'tickets' => "Failed to reserve tickets for '{$ticket->name}'."
                    ]);
                }

                $itemTotal = $ticket->price * $quantity;
                $subtotal += $itemTotal;

                $orderItems[] = [
                    'ticket_id' => $ticket->id,
                    'quantity' => $quantity,
                    'unit_price' => $ticket->price,
                    'total_price' => $itemTotal,
                ];
            }

            // Calculate platform fee (10%)
            $platformFee = $subtotal * 0.1;
            $totalAmount = $subtotal + $platformFee;

            // Create order
            $order = Order::create([
                'user_id' => Auth::id(),
                'event_id' => $event->id,
                'buyer_name' => $validated['buyer_name'],
                'buyer_email' => $validated['buyer_email'],
                'buyer_phone' => $validated['buyer_phone'],
                'subtotal' => $subtotal,
                'platform_fee' => $platformFee,
                'total_amount' => $totalAmount,
                'status' => 'pending',
                'payment_method' => 'paystack',
                'payment_status' => 'pending',
                'custom_form_data' => $validated['custom_form_data'] ?? null,
                'notes' => $validated['notes'] ?? null,
            ]);

            // Create order items
            foreach ($orderItems as $itemData) {
                $itemData['order_id'] = $order->id;
                OrderItem::create($itemData);
            }

            // Check if this is a non-Inertia AJAX/API request
            if ((request()->expectsJson() || request()->ajax()) && !request()->header('X-Inertia')) {
                return response()->json([
                    'success' => true,
                    'message' => 'Order created successfully!',
                    'order' => $order->load('items.ticket'),
                    'payment_url' => route('orders.payment', $order->order_number)
                ]);
            }

            // For Inertia requests, redirect to payment page
            return redirect()->route('orders.payment', $order->order_number);
        });
    }

    /**
     * Show payment page for order
     */
    public function showPayment(Order $order)
    {
        // Check if order is still pending
        if ($order->status !== 'pending') {
            return redirect()->route('orders.show', $order->order_number);
        }

        $paystackService = new PaystackService();

        // Initialize payment with Paystack
        $paymentResult = $paystackService->initializePayment($order);

        if (!$paymentResult['status']) {
            return redirect()->route('event.tickets', $order->event->slug)
                ->with('error', 'Payment initialization failed. Please try again.');
        }

        return Inertia::render('Ticket/Payment', [
            'order' => $order->load(['items.ticket', 'event']),
            'paystack' => [
                'public_key' => $paystackService->getPublicKey(),
                'authorization_url' => $paymentResult['authorization_url'],
                'access_code' => $paymentResult['access_code'],
                'reference' => $paymentResult['reference'],
            ],
        ]);
    }

    /**
     * Show order details/ticket
     */
    public function show(Order $order)
    {
        // Check if user can view this order
        if ($order->user_id && $order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this order.');
        }

        return Inertia::render('Ticket/OrderDetails', [
            'order' => $order->load(['items.ticket', 'event.organizer']),
        ]);
    }

    /**
     * Handle payment success callback
     */
    public function paymentSuccess(Request $request, Order $order)
    {
        // Get reference from request (works for both GET and POST)
        $reference = $request->get('reference') ?: $request->query('reference') ?: $order->payment_reference;

        if (!$reference) {
            Log::warning('Payment success callback without reference', [
                'order_id' => $order->id,
                'request_data' => $request->all(),
                'query_params' => $request->query(),
            ]);

            return redirect()->route('event.tickets', $order->event->slug)
                ->with('error', 'Payment reference not found.');
        }

        // Check if payment is already processed
        if ($order->payment_status === 'paid' && $order->status === 'completed') {
            Log::info('Payment already processed, redirecting to success page', [
                'order_id' => $order->id,
                'reference' => $reference,
            ]);

            return Inertia::render('Ticket/OrderSuccess', [
                'order' => $order->load(['items.ticket', 'event.organizer']),
            ]);
        }

        $paystackService = new PaystackService();
        $verificationResult = $paystackService->verifyPayment($reference);

        if (!$verificationResult['status']) {
            return redirect()->route('orders.payment', $order->order_number)
                ->with('error', 'Payment verification failed. Please try again.');
        }

        $paymentData = $verificationResult['data'];

        // Check if payment was successful
        if ($paymentData['status'] !== 'success') {
            return redirect()->route('orders.payment', $order->order_number)
                ->with('error', 'Payment was not successful. Please try again.');
        }

        // Verify amount matches
        $expectedAmount = $order->total_amount * 100; // Convert to kobo
        if ($paymentData['amount'] != $expectedAmount) {
            Log::error('Payment amount mismatch', [
                'expected' => $expectedAmount,
                'received' => $paymentData['amount'],
                'order_id' => $order->id,
            ]);
            return redirect()->route('orders.payment', $order->order_number)
                ->with('error', 'Payment amount verification failed.');
        }

        // Update order status
        $order->update([
            'payment_reference' => $reference,
            'payment_status' => 'paid',
            'status' => 'completed',
        ]);

        // Mark order as completed (this will update event attendee count)
        $order->markAsCompleted();

        // Generate QR code
        $order->update([
            'qr_code' => $order->generateQrCode()
        ]);

        return Inertia::render('Ticket/OrderSuccess', [
            'order' => $order->load(['items.ticket', 'event.organizer']),
        ]);
    }

    /**
     * Handle payment failure callback
     */
    public function paymentFailed(Request $request, Order $order)
    {
        // Mark order as failed and release tickets
        $order->markAsFailed();

        return redirect()->route('event.tickets', $order->event->slug)
            ->with('error', 'Payment failed. Please try again.');
    }

    /**
     * Download ticket as PDF
     */
    public function downloadTicket(Order $order)
    {
        // Check if user can download this ticket
        if ($order->user_id && $order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this ticket.');
        }

        // Check if order is completed
        if (!$order->isCompleted()) {
            abort(404, 'Ticket not found or payment not completed.');
        }

        // Update downloaded timestamp
        if (!$order->downloaded_at) {
            $order->update(['downloaded_at' => now()]);
        }

        // Load order with relationships
        $order->load(['items.ticket', 'event.organizer']);

        // Generate QR code if not exists
        if (!$order->qr_code) {
            $order->update(['qr_code' => $order->generateQrCode()]);
        }

        // Generate PDF ticket
        $pdf = Pdf::loadView('tickets.pdf', compact('order'));

        // Set paper size and orientation
        $pdf->setPaper('A4', 'portrait');

        // Set options for better rendering
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isPhpEnabled' => true,
            'defaultFont' => 'Arial',
            'dpi' => 150,
            'defaultPaperSize' => 'A4',
            'chroot' => public_path(),
        ]);

        // Generate filename
        $filename = 'ticket-' . $order->order_number . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Send ticket via email
     */
    public function sendTicketEmail(Order $order, Request $request)
    {
        Log::info('Email sending request received', [
            'order_id' => $order->id,
            'order_number' => $order->order_number,
            'request_data' => $request->all()
        ]);

        $validated = $request->validate([
            'email' => 'required|email',
        ]);

        Log::info('Email validation passed', [
            'order_id' => $order->id,
            'target_email' => $validated['email']
        ]);

        try {
            // Load order with relationships for the email
            $order->load(['items.ticket', 'event.organizer']);

            Log::info('Attempting to send ticket email', [
                'order_id' => $order->id,
                'target_email' => $validated['email'],
                'event_title' => $order->event->title
            ]);

            // Send email notification with ticket details
            Notification::route('mail', $validated['email'])
                ->notify(new TicketEmailNotification($order));

            Log::info('Ticket email sent successfully', [
                'order_id' => $order->id,
                'target_email' => $validated['email']
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Ticket sent successfully to ' . $validated['email']
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send ticket email', [
                'order_id' => $order->id,
                'email' => $validated['email'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send email. Please try again.'
            ], 500);
        }
    }

    /**
     * Verify ticket with QR code
     */
    public function verifyTicket(Request $request)
    {
        $validated = $request->validate([
            'qr_code' => 'required|string',
        ]);

        $qrCode = $validated['qr_code'];

        // Parse QR code (format: ORDER_NUMBER|HASH)
        $parts = explode('|', $qrCode);
        if (count($parts) !== 2) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid QR code format.'
            ], 400);
        }

        [$orderNumber, $providedHash] = $parts;

        $order = Order::where('order_number', $orderNumber)
            ->where('status', 'completed')
            ->with(['items.ticket', 'event', 'event.organizer'])
            ->first();

        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired ticket.'
            ], 404);
        }

        // Verify the hash
        $verificationString = $order->order_number . '|' . $order->event_id . '|' . $order->total_amount;
        $expectedHash = substr(hash('sha256', $verificationString . config('app.key')), 0, 8);

        if ($providedHash !== $expectedHash) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid ticket verification code.'
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => 'Valid ticket.',
            'order' => $order,
            'verified_at' => now()->toISOString(),
        ]);
    }

    /**
     * Handle Paystack webhook
     */
    public function paystackWebhook(Request $request)
    {
        // Get the payload
        $payload = $request->getContent();
        $signature = $request->header('x-paystack-signature');

        if (!$signature) {
            Log::error('Paystack webhook: Missing signature');
            return response()->json(['error' => 'Missing signature'], 400);
        }

        $paystackService = new PaystackService();

        // Validate webhook signature
        if (!$paystackService->validateWebhookSignature($payload, $signature)) {
            Log::error('Paystack webhook: Invalid signature');
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        // Process the webhook
        $data = json_decode($payload, true);
        $result = $paystackService->handleWebhook($data);

        if ($result['status']) {
            return response()->json(['message' => $result['message']], 200);
        } else {
            return response()->json(['error' => $result['message']], 400);
        }
    }
}
