<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ticket - {{ $order->event->title }}</title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .ticket-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .ticket-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .banner-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.3;
        }
        
        .header-content {
            position: relative;
            z-index: 2;
        }
        
        .event-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .order-number {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 25px;
        }
        
        .ticket-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .status-badge {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            border: 2px solid rgba(16, 185, 129, 0.3);
        }
        
        .total-amount {
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .ticket-body {
            padding: 40px;
        }
        
        .event-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .detail-item {
            margin-bottom: 20px;
        }
        
        .detail-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .detail-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
        }
        
        .tickets-section {
            border-top: 3px solid #f0f0f0;
            padding-top: 40px;
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 25px;
            color: #333;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: '🎫';
            margin-right: 10px;
            font-size: 28px;
        }
        
        .ticket-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .ticket-item:last-child {
            margin-bottom: 0;
        }
        
        .ticket-name {
            font-weight: 700;
            font-size: 18px;
            color: #333;
        }
        
        .ticket-quantity {
            color: #666;
            font-size: 14px;
            margin-top: 4px;
        }
        
        .ticket-price {
            font-weight: 700;
            font-size: 18px;
            color: #667eea;
        }
        
        .qr-section {
            text-align: center;
            border-top: 3px solid #f0f0f0;
            padding-top: 40px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            margin: 0 -40px -40px -40px;
            padding-left: 40px;
            padding-right: 40px;
            padding-bottom: 40px;
        }
        
        .qr-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .qr-title::before {
            content: '📱';
            margin-right: 10px;
            font-size: 28px;
        }
        
        .qr-code {
            width: 180px;
            height: 180px;
            margin: 0 auto 20px;
            border: 3px solid #667eea;
            border-radius: 12px;
            padding: 15px;
            background: white;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        
        .qr-instructions {
            font-size: 16px;
            color: #666;
            max-width: 500px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .footer {
            background: #333;
            color: white;
            padding: 30px 40px;
            text-align: center;
        }
        
        .footer-text {
            font-size: 14px;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 80px;
            color: rgba(102, 126, 234, 0.03);
            font-weight: bold;
            z-index: 1;
            pointer-events: none;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #5a67d8;
        }
        
        @media (max-width: 768px) {
            .event-details {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .ticket-body {
                padding: 20px;
            }
            
            .ticket-header {
                padding: 20px;
            }
            
            .event-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">🖨️ Print/Save as PDF</button>
    
    <div class="ticket-container">
        <!-- Header with Event Banner -->
        <div class="ticket-header">
            @if($order->event->banner_image)
                @php
                    $bannerUrl = $order->event->banner_image;
                    // Ensure the URL is absolute
                    if (!str_starts_with($bannerUrl, 'http')) {
                        $bannerUrl = asset('storage/' . ltrim($bannerUrl, '/'));
                    }
                @endphp
                <img src="{{ $bannerUrl }}" alt="{{ $order->event->title }}" class="banner-image" onerror="this.style.display='none'">
            @endif
            
            <div class="header-content">
                <h1 class="event-title">{{ $order->event->title }}</h1>
                <p class="order-number">Order #{{ $order->order_number }}</p>
                
                <div class="ticket-info">
                    <div class="status-badge">✓ Confirmed</div>
                    <div>
                        <div class="total-amount">₦{{ number_format($order->total_amount, 2) }}</div>
                        <div style="font-size: 16px; opacity: 0.9;">Total Paid</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Details -->
        <div class="ticket-body">
            <div class="event-details">
                <div>
                    <div class="detail-item">
                        <div class="detail-label">📅 Date & Time</div>
                        <div class="detail-value">
                            {{ \Carbon\Carbon::parse($order->event->event_date)->format('l, F j, Y') }}<br>
                            {{ \Carbon\Carbon::parse($order->event->event_time)->format('g:i A') }}
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">📍 Venue</div>
                        <div class="detail-value">
                            {{ $order->event->venue }}<br>
                            {{ $order->event->city }}, {{ $order->event->state }}
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="detail-item">
                        <div class="detail-label">👤 Attendee</div>
                        <div class="detail-value">{{ $order->buyer_name }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">📧 Email</div>
                        <div class="detail-value">{{ $order->buyer_email }}</div>
                    </div>
                    
                    @if($order->buyer_phone)
                    <div class="detail-item">
                        <div class="detail-label">📞 Phone</div>
                        <div class="detail-value">{{ $order->buyer_phone }}</div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Tickets Section -->
            <div class="tickets-section">
                <h2 class="section-title">Your Tickets</h2>
                @foreach($order->items as $item)
                <div class="ticket-item">
                    <div>
                        <div class="ticket-name">{{ $item->ticket->name }}</div>
                        <div class="ticket-quantity">Quantity: {{ $item->quantity }}</div>
                    </div>
                    <div class="ticket-price">₦{{ number_format($item->total_price, 2) }}</div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- QR Code Section -->
        <div class="qr-section">
            <h3 class="qr-title">Entry Code</h3>
            @if($order->qr_code)
                <div class="qr-code" style="display: flex; flex-direction: column; align-items: center; justify-content: center; background: #f8f9fa; border: 3px dashed #667eea;">
                    <div style="font-size: 14px; color: #666; margin-bottom: 10px;">Entry Code:</div>
                    <div style="font-family: 'Courier New', monospace; font-size: 18px; font-weight: bold; color: #333; background: white; padding: 15px; border-radius: 8px; border: 2px solid #667eea;">
                        {{ $order->qr_code }}
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 10px;">Order: {{ $order->order_number }}</div>
                </div>
            @else
                <div class="qr-code" style="display: flex; flex-direction: column; align-items: center; justify-content: center; background: #f8f9fa; border: 3px dashed #667eea;">
                    <div style="font-size: 14px; color: #666; margin-bottom: 10px;">Entry Code:</div>
                    <div style="font-family: 'Courier New', monospace; font-size: 18px; font-weight: bold; color: #333; background: white; padding: 15px; border-radius: 8px; border: 2px solid #667eea;">
                        {{ $order->order_number }}
                    </div>
                    <div style="font-size: 12px; color: #666; margin-top: 10px;">Order Number</div>
                </div>
            @endif
            <p class="qr-instructions">
                📱 Present this entry code at the event entrance for check-in.<br>
                ⏰ Please arrive at least 30 minutes before the event starts.<br>
                💾 Save this page as PDF for offline access.
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="logo">TickGet</div>
            <p class="footer-text">
                🎫 This ticket is valid only for the specified event date and time.<br>
                🚫 No refunds or exchanges allowed unless event is cancelled.
            </p>
            <p class="footer-text">Powered by TickGet - Your Premier Event Ticketing Platform</p>
        </div>

        <!-- Watermark -->
         
        <div class="watermark">TICKGET</div>
    </div>

    <script>
        // Auto-focus print dialog when page loads
        window.addEventListener('load', function() {
            // Small delay to ensure page is fully rendered
            setTimeout(function() {
                if (window.location.search.includes('auto-print')) {
                    window.print();
                }
            }, 500);
        });
    </script>
</body>
</html>
