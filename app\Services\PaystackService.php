<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Models\Order;

class PaystackService
{
    protected $secretKey;
    protected $publicKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->secretKey = config('services.paystack.secret_key');
        $this->publicKey = config('services.paystack.public_key');
        $this->baseUrl = config('services.paystack.payment_url');
    }

    /**
     * Initialize a payment transaction
     */
    public function initializePayment(Order $order, string $callbackUrl = null)
    {
        $url = $this->baseUrl . '/transaction/initialize';
        
        $data = [
            'email' => $order->buyer_email,
            'amount' => $order->total_amount * 100, // Convert to kobo
            'reference' => $order->order_number,
            'callback_url' => $callbackUrl ?: route('orders.payment.success', $order->order_number),
            'metadata' => [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'event_title' => $order->event->title,
                'buyer_name' => $order->buyer_name,
                'buyer_phone' => $order->buyer_phone,
                'platform_fee' => $order->platform_fee * 100,
                'subtotal' => $order->subtotal * 100,
            ],
            'channels' => ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer'],
        ];

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($url, $data);

            if ($response->successful()) {
                $responseData = $response->json();
                
                if ($responseData['status'] === true) {
                    // Update order with payment reference
                    $order->update([
                        'payment_reference' => $responseData['data']['reference'],
                    ]);

                    return [
                        'status' => true,
                        'data' => $responseData['data'],
                        'authorization_url' => $responseData['data']['authorization_url'],
                        'access_code' => $responseData['data']['access_code'],
                        'reference' => $responseData['data']['reference'],
                    ];
                }
            }

            Log::error('Paystack initialization failed', [
                'response' => $response->json(),
                'order_id' => $order->id,
            ]);

            return [
                'status' => false,
                'message' => $response->json()['message'] ?? 'Payment initialization failed',
            ];

        } catch (\Exception $e) {
            Log::error('Paystack initialization error', [
                'error' => $e->getMessage(),
                'order_id' => $order->id,
            ]);

            return [
                'status' => false,
                'message' => 'Payment service unavailable. Please try again.',
            ];
        }
    }

    /**
     * Verify a payment transaction
     */
    public function verifyPayment(string $reference)
    {
        $url = $this->baseUrl . '/transaction/verify/' . $reference;

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
            ])->get($url);

            if ($response->successful()) {
                $responseData = $response->json();
                
                if ($responseData['status'] === true) {
                    return [
                        'status' => true,
                        'data' => $responseData['data'],
                    ];
                }
            }

            Log::error('Paystack verification failed', [
                'response' => $response->json(),
                'reference' => $reference,
            ]);

            return [
                'status' => false,
                'message' => $response->json()['message'] ?? 'Payment verification failed',
            ];

        } catch (\Exception $e) {
            Log::error('Paystack verification error', [
                'error' => $e->getMessage(),
                'reference' => $reference,
            ]);

            return [
                'status' => false,
                'message' => 'Payment verification failed. Please contact support.',
            ];
        }
    }

    /**
     * Handle webhook from Paystack
     */
    public function handleWebhook(array $payload)
    {
        try {
            $event = $payload['event'];
            $data = $payload['data'];

            switch ($event) {
                case 'charge.success':
                    return $this->handleSuccessfulPayment($data);
                
                case 'charge.failed':
                    return $this->handleFailedPayment($data);
                
                default:
                    Log::info('Unhandled Paystack webhook event', ['event' => $event]);
                    return ['status' => true, 'message' => 'Event not handled'];
            }

        } catch (\Exception $e) {
            Log::error('Paystack webhook error', [
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);

            return ['status' => false, 'message' => 'Webhook processing failed'];
        }
    }

    /**
     * Handle successful payment
     */
    protected function handleSuccessfulPayment(array $data)
    {
        $reference = $data['reference'];
        $order = Order::where('payment_reference', $reference)->first();

        if (!$order) {
            Log::error('Order not found for payment reference', ['reference' => $reference]);
            return ['status' => false, 'message' => 'Order not found'];
        }

        if ($order->status === 'completed') {
            return ['status' => true, 'message' => 'Order already processed'];
        }

        // Verify the payment amount matches
        $expectedAmount = $order->total_amount * 100; // Convert to kobo
        if ($data['amount'] != $expectedAmount) {
            Log::error('Payment amount mismatch', [
                'expected' => $expectedAmount,
                'received' => $data['amount'],
                'order_id' => $order->id,
            ]);
            return ['status' => false, 'message' => 'Payment amount mismatch'];
        }

        // Mark order as completed
        $order->markAsCompleted();

        Log::info('Payment processed successfully', [
            'order_id' => $order->id,
            'reference' => $reference,
            'amount' => $data['amount'],
        ]);

        return ['status' => true, 'message' => 'Payment processed successfully'];
    }

    /**
     * Handle failed payment
     */
    protected function handleFailedPayment(array $data)
    {
        $reference = $data['reference'];
        $order = Order::where('payment_reference', $reference)->first();

        if (!$order) {
            Log::error('Order not found for failed payment', ['reference' => $reference]);
            return ['status' => false, 'message' => 'Order not found'];
        }

        // Mark order as failed
        $order->markAsFailed();

        Log::info('Payment failed', [
            'order_id' => $order->id,
            'reference' => $reference,
            'reason' => $data['gateway_response'] ?? 'Unknown',
        ]);

        return ['status' => true, 'message' => 'Failed payment processed'];
    }

    /**
     * Get public key for frontend
     */
    public function getPublicKey()
    {
        return $this->publicKey;
    }

    /**
     * Validate webhook signature
     */
    public function validateWebhookSignature(string $payload, string $signature)
    {
        $computedSignature = hash_hmac('sha512', $payload, $this->secretKey);
        return hash_equals($signature, $computedSignature);
    }
}
