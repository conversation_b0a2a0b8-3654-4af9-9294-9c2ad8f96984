{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"axios": "^1.6.4", "laravel-vite-plugin": "^1.0", "vite": "^5.0"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@inertiajs/react": "^2.0.12", "@tailwindcss/vite": "^4.1.9", "@vitejs/plugin-react": "^4.5.2", "chart.js": "^4.4.9", "framer-motion": "^12.17.0", "qrcode": "^1.5.4", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "tailwindcss": "^4.1.9"}}