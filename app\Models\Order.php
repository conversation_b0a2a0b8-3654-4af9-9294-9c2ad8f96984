<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Notification;
use App\Notifications\OrderConfirmationNotification;
use App\Notifications\PaymentSuccessNotification;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event_id',
        'order_number',
        'buyer_name',
        'buyer_email',
        'buyer_phone',
        'subtotal',
        'platform_fee',
        'total_amount',
        'status',
        'payment_method',
        'payment_reference',
        'payment_status',
        'custom_form_data',
        'notes',
        'qr_code',
        'downloaded_at',
        'checked_in',
        'checked_in_at',
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'platform_fee' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'custom_form_data' => 'array',
        'downloaded_at' => 'datetime',
        'checked_in' => 'boolean',
        'checked_in_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = 'TG-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the user who made this order
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the event this order is for
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get order items
     */
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Calculate platform fee (10%)
     */
    public function calculatePlatformFee(): float
    {
        return $this->subtotal * 0.1;
    }

    /**
     * Calculate total amount including platform fee
     */
    public function calculateTotal(): float
    {
        return $this->subtotal + $this->calculatePlatformFee();
    }

    /**
     * Mark order as completed
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'payment_status' => 'paid',
        ]);

        // Update event attendee count
        $totalTickets = $this->items()->sum('quantity');
        $this->event->increment('current_attendees', $totalTickets);

        // Send notifications
        $this->sendOrderConfirmationNotification();
        $this->sendPaymentSuccessNotification();
    }

    /**
     * Send order confirmation notification
     */
    public function sendOrderConfirmationNotification(): void
    {
        // Use Notification::route() to send to email directly
        Notification::route('mail', $this->buyer_email)
            ->notify(new OrderConfirmationNotification($this));
    }

    /**
     * Send payment success notification
     */
    public function sendPaymentSuccessNotification(): void
    {
        // Use Notification::route() to send to email directly
        Notification::route('mail', $this->buyer_email)
            ->notify(new PaymentSuccessNotification($this));
    }

    /**
     * Mark order as failed
     */
    public function markAsFailed(): void
    {
        $this->update([
            'status' => 'failed',
            'payment_status' => 'failed',
        ]);

        // Release reserved tickets
        foreach ($this->items as $item) {
            $item->ticket->release($item->quantity);
        }
    }

    /**
     * Generate QR code for the order
     */
    public function generateQrCode(): string
    {
        // Create QR code data with order verification info
        $qrData = [
            'order_number' => $this->order_number,
            'event_id' => $this->event_id,
            'buyer_email' => $this->buyer_email,
            'total_amount' => $this->total_amount,
            'verification_url' => route('tickets.verify'),
            'timestamp' => now()->timestamp,
        ];

        // Create a verification hash
        $verificationString = $this->order_number . '|' . $this->event_id . '|' . $this->total_amount;
        $hash = hash('sha256', $verificationString . config('app.key'));

        // Return the order number with hash for QR code
        return $this->order_number . '|' . substr($hash, 0, 8);
    }

    /**
     * Check if order is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if order is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Get formatted total amount
     */
    public function getFormattedTotal(): string
    {
        return '₦' . number_format($this->total_amount, 2);
    }

    /**
     * Scope for completed orders
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending orders
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }
}
