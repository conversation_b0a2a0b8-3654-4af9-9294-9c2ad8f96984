<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Withdrawal;

class WithdrawalStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $withdrawal;
    protected $status;

    /**
     * Create a new notification instance.
     */
    public function __construct(Withdrawal $withdrawal, string $status)
    {
        $this->withdrawal = $withdrawal;
        $this->status = $status;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $withdrawal = $this->withdrawal;
        $statusTitle = ucfirst($this->status);
        
        $message = (new MailMessage)
            ->subject("Withdrawal Request {$statusTitle} - {$withdrawal->reference_number}")
            ->greeting('Hello ' . $withdrawal->user->name . '!');

        switch ($this->status) {
            case 'approved':
                $message->line('Good news! Your withdrawal request has been approved.')
                    ->line('Your funds will be processed and transferred to your bank account within 1-3 business days.')
                    ->line('')
                    ->line('**Withdrawal Details:**')
                    ->line('Reference: ' . $withdrawal->reference_number)
                    ->line('Amount: ₦' . number_format($withdrawal->amount, 2))
                    ->line('Bank Account: ' . $withdrawal->bank_name . ' - ****' . substr($withdrawal->account_number, -4))
                    ->line('Approved Date: ' . $withdrawal->updated_at->format('F j, Y \a\t g:i A'));
                
                if ($withdrawal->admin_notes) {
                    $message->line('Admin Notes: ' . $withdrawal->admin_notes);
                }
                
                $message->line('')
                    ->line('You will receive another notification once the transfer is completed.')
                    ->action('View Withdrawal History', route('withdrawals.index'));
                break;

            case 'rejected':
                $message->line('We regret to inform you that your withdrawal request has been rejected.')
                    ->line('')
                    ->line('**Withdrawal Details:**')
                    ->line('Reference: ' . $withdrawal->reference_number)
                    ->line('Amount: ₦' . number_format($withdrawal->amount, 2))
                    ->line('Rejected Date: ' . $withdrawal->updated_at->format('F j, Y \a\t g:i A'));
                
                if ($withdrawal->admin_notes) {
                    $message->line('Reason: ' . $withdrawal->admin_notes);
                }
                
                $message->line('')
                    ->line('If you believe this is an error, please contact our support team.')
                    ->action('Contact Support', route('withdrawals.index'));
                break;

            case 'completed':
                $message->line('Great news! Your withdrawal has been completed successfully.')
                    ->line('The funds have been transferred to your bank account.')
                    ->line('')
                    ->line('**Transfer Details:**')
                    ->line('Reference: ' . $withdrawal->reference_number)
                    ->line('Amount: ₦' . number_format($withdrawal->amount, 2))
                    ->line('Bank Account: ' . $withdrawal->bank_name . ' - ****' . substr($withdrawal->account_number, -4))
                    ->line('Completed Date: ' . $withdrawal->updated_at->format('F j, Y \a\t g:i A'));
                
                if ($withdrawal->admin_notes) {
                    $message->line('Notes: ' . $withdrawal->admin_notes);
                }
                
                $message->line('')
                    ->line('Please allow up to 24 hours for the funds to reflect in your account.')
                    ->action('View Withdrawal History', route('withdrawals.index'));
                break;
        }

        return $message->salutation('Best regards, The TickGet Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'withdrawal_id' => $this->withdrawal->id,
            'reference_number' => $this->withdrawal->reference_number,
            'amount' => $this->withdrawal->amount,
            'status' => $this->status,
            'admin_notes' => $this->withdrawal->admin_notes,
        ];
    }
}
