<?php

namespace App\Http\Controllers;

use App\Models\PasswordResetToken;
use App\Models\User;
use App\Notifications\PasswordResetNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class ForgotPasswordController extends Controller
{
    /**
     * Show the forgot password form
     */
    public function showForgotPasswordForm()
    {
        return Inertia::render('Auth/ForgotPassword');
    }

    /**
     * Send password reset email
     */
    public function sendResetEmail(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email'
            ]);

            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'We could not find a user with that email address.'
                ], 422);
            }

            // Create password reset token
            $resetToken = PasswordResetToken::createToken($request->email);

            // Log the token for testing (remove in production)
            Log::info('Password reset token created', [
                'email' => $request->email,
                'token' => $resetToken->token
            ]);

            // Send email notification
            try {
                $user->notify(new PasswordResetNotification($resetToken->token));
                Log::info('Password reset email sent successfully', ['email' => $request->email]);
            } catch (\Exception $e) {
                Log::error('Failed to send password reset email', [
                    'email' => $request->email,
                    'error' => $e->getMessage()
                ]);

                // Still return success but with a note about email
                return response()->json([
                    'success' => true,
                    'message' => 'Email delivery failed, but your reset code is: ' . $resetToken->token . '. Please use this code to reset your password.',
                    'token' => $resetToken->token,
                    'email_failed' => true
                ]);
            }

            // In development, also show the token for easy testing
            $message = 'Password reset code has been sent to your email address.';
            $responseData = [
                'success' => true,
                'message' => $message
            ];

            // Add token to response in development mode
            if (config('app.env') === 'local') {
                $responseData['debug_token'] = $resetToken->token;
                $responseData['message'] .= ' (Debug: Your reset code is ' . $resetToken->token . ')';
            }

            return response()->json($responseData);
        } catch (\Exception $e) {
            Log::error('Error in sendResetEmail', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred. Please try again.'
            ], 500);
        }
    }

    /**
     * Show the reset password form
     */
    public function showResetPasswordForm()
    {
        return Inertia::render('Auth/ResetPassword');
    }

    /**
     * Verify reset token
     */
    public function verifyToken(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'token' => 'required|string|size:7'
        ]);

        $resetToken = PasswordResetToken::findValidToken(
            $request->email,
            strtoupper($request->token)
        );

        if (!$resetToken) {
            throw ValidationException::withMessages([
                'token' => 'Invalid or expired reset code.',
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Reset code verified successfully.'
        ]);
    }

    /**
     * Reset password
     */
    public function resetPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'token' => 'required|string|size:7',
            'password' => 'required|string|min:8|confirmed'
        ]);

        $resetToken = PasswordResetToken::findValidToken(
            $request->email,
            strtoupper($request->token)
        );

        if (!$resetToken) {
            throw ValidationException::withMessages([
                'token' => 'Invalid or expired reset code.',
            ]);
        }

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            throw ValidationException::withMessages([
                'email' => 'User not found.',
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($request->password)
        ]);

        // Mark token as used
        $resetToken->markAsUsed();

        return response()->json([
            'success' => true,
            'message' => 'Password has been reset successfully. You can now login with your new password.'
        ]);
    }
}
