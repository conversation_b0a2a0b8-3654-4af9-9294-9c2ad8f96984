<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Event;
use App\Models\Ticket;
use App\Models\Order;
use App\Models\Withdrawal;

class TicketPlatformTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->organizer = User::factory()->create([
            'role' => 'organizer',
            'email' => '<EMAIL>',
        ]);

        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function guest_can_view_public_events()
    {
        // Create a public event
        $event = Event::factory()->create([
            'organizer_id' => $this->organizer->id,
            'is_active' => true,
            'event_date' => now()->addDays(30),
        ]);

        // Test public home page
        $response = $this->get('/');
        $response->assertStatus(200);

        // Test public events page
        $response = $this->get('/events');
        $response->assertStatus(200);

        // Test event details page
        $response = $this->get('/events/' . $event->slug);
        $response->assertStatus(200);
    }

    /** @test */
    public function organizer_can_create_event()
    {
        $this->actingAs($this->organizer);

        $eventData = [
            'title' => 'Test Event',
            'description' => 'This is a test event description.',
            'short_description' => 'Test event',
            'event_date' => now()->addDays(30)->format('Y-m-d'),
            'event_time' => '18:00',
            'venue' => 'Test Venue',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'category' => 'Conference',
            'max_attendees' => 100,
        ];

        $response = $this->post('/events', $eventData);
        $response->assertRedirect();

        $this->assertDatabaseHas('events', [
            'title' => 'Test Event',
            'organizer_id' => $this->organizer->id,
        ]);
    }

    /** @test */
    public function organizer_can_create_tickets_for_event()
    {
        $this->actingAs($this->organizer);

        $event = Event::factory()->create([
            'organizer_id' => $this->organizer->id,
        ]);

        $ticketData = [
            'name' => 'General Admission',
            'description' => 'General admission ticket',
            'price' => 5000,
            'quantity_available' => 50,
            'is_active' => true,
        ];

        $response = $this->post("/events/{$event->id}/tickets", $ticketData);
        $response->assertRedirect();

        $this->assertDatabaseHas('tickets', [
            'event_id' => $event->id,
            'name' => 'General Admission',
            'price' => 5000,
        ]);
    }

    /** @test */
    public function user_can_view_dashboard_with_analytics()
    {
        $this->actingAs($this->organizer);

        // Create some test data
        $event = Event::factory()->create([
            'organizer_id' => $this->organizer->id,
        ]);

        $response = $this->get('/dashboard');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('User/Dashboard')
                ->has('analytics')
        );
    }

    /** @test */
    public function organizer_can_request_withdrawal()
    {
        $this->actingAs($this->organizer);

        // Update user with bank details
        $this->organizer->update([
            'bank_name' => 'Test Bank',
            'account_number' => '**********',
            'account_name' => 'Test User',
        ]);

        $withdrawalData = [
            'amount' => 10000,
            'bank_name' => 'Test Bank',
            'account_number' => '**********',
            'account_name' => 'Test User',
        ];

        $response = $this->post('/withdrawals', $withdrawalData);
        $response->assertRedirect();

        $this->assertDatabaseHas('withdrawals', [
            'user_id' => $this->organizer->id,
            'amount' => 10000,
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function admin_can_view_dashboard()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/admin/dashboard');
        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->component('Admin/Dashboard')
        );
    }

    /** @test */
    public function admin_can_manage_withdrawals()
    {
        $this->actingAs($this->admin);

        $withdrawal = Withdrawal::factory()->create([
            'user_id' => $this->organizer->id,
            'status' => 'pending',
        ]);

        // Test approve withdrawal
        $response = $this->patch("/admin/withdrawals/{$withdrawal->id}/approve", [
            'admin_notes' => 'Approved for testing',
        ]);
        $response->assertRedirect();

        $withdrawal->refresh();
        $this->assertEquals('approved', $withdrawal->status);
    }

    /** @test */
    public function user_can_update_profile()
    {
        $this->actingAs($this->organizer);

        $profileData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '+234 ************',
            'location' => 'Lagos, Nigeria',
            'bio' => 'Updated bio',
            'organization_name' => 'Test Organization',
        ];

        $response = $this->put('/profile', $profileData);
        $response->assertRedirect();

        $this->organizer->refresh();
        $this->assertEquals('Updated Name', $this->organizer->name);
        $this->assertEquals('<EMAIL>', $this->organizer->email);
    }

    /** @test */
    public function event_search_works_correctly()
    {
        // Create test events
        $event1 = Event::factory()->create([
            'title' => 'Tech Conference 2024',
            'category' => 'Conference',
            'city' => 'Lagos',
            'is_active' => true,
            'event_date' => now()->addDays(30),
        ]);

        $event2 = Event::factory()->create([
            'title' => 'Music Festival',
            'category' => 'Music',
            'city' => 'Abuja',
            'is_active' => true,
            'event_date' => now()->addDays(45),
        ]);

        // Test search by title
        $response = $this->get('/events/search?q=Tech');
        $response->assertStatus(200);

        // Test filter by category
        $response = $this->get('/events?category=Conference');
        $response->assertStatus(200);

        // Test filter by city
        $response = $this->get('/events?city=Lagos');
        $response->assertStatus(200);
    }

    /** @test */
    public function file_upload_service_validation_works()
    {
        $this->actingAs($this->organizer);

        // Test with invalid file type
        $response = $this->post('/events', [
            'title' => 'Test Event',
            'description' => 'Test description',
            'event_date' => now()->addDays(30)->format('Y-m-d'),
            'event_time' => '18:00',
            'venue' => 'Test Venue',
            'address' => '123 Test Street',
            'city' => 'Lagos',
            'state' => 'Lagos',
            'banner_image' => \Illuminate\Http\UploadedFile::fake()->create('test.txt', 100),
        ]);

        $response->assertSessionHasErrors('banner_image');
    }
}
