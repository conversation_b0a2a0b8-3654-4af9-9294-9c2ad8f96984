# TickGet - Frontend Documentation

## Overview
TickGet is a comprehensive ticket selling platform built with Laravel, Inertia.js, React, and Tailwind CSS. This frontend provides a premium user experience with smooth animations, responsive design, and intuitive interfaces for event organizers, ticket buyers, and administrators.

## Tech Stack
- **Framework**: Laravel + Inertia.js + React
- **Styling**: Tailwind CSS
- **Icons**: Heroicons
- **Animations**: Framer Motion
- **Charts**: Chart.js + React Chart.js 2
- **Forms**: React Hook Form
- **UI Components**: Headless UI

## Project Structure

```
resources/js/
├── Layouts/
│   ├── GuestLayout.jsx      # Landing page, login, register
│   ├── AppLayout.jsx        # User dashboard with sidebar
│   ├── TicketLayout.jsx     # Ticket display pages
│   └── AdminLayout.jsx      # Admin dashboard
├── Components/
│   ├── Header.jsx           # Main navigation header
│   ├── Footer.jsx           # Site footer
│   ├── Modal.jsx            # Reusable modal component
│   └── Notifications/
│       ├── SuccessAlert.jsx # Success notifications
│       └── ErrorAlert.jsx   # Error notifications
└── Pages/
    ├── Guest/
    │   ├── Home.jsx         # 5-section landing page
    │   ├── Login.jsx        # User authentication
    │   └── Register.jsx     # User registration with role selection
    ├── User/
    │   ├── Dashboard.jsx    # Analytics dashboard with charts
    │   ├── AddEvent.jsx     # Event creation form
    │   └── Withdraw.jsx     # Earnings withdrawal system
    ├── Ticket/
    │   └── EventTickets.jsx # Dynamic ticket purchasing page
    └── Admin/
        └── Dashboard.jsx    # Admin analytics and management
```

## Key Features

### 🎨 Design System
- **Premium Visual Design**: Every element feels premium with consistent spacing and cohesive color palette
- **Subtle Animations**: Framer Motion animations enhance user experience without being distracting
- **Responsive Design**: Mobile-first approach ensuring perfect display on all devices
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation

### 🏠 Landing Page (5 Sections)
1. **Hero Section**: Animated hero with compelling CTA and floating elements
2. **Features Section**: Showcase of platform capabilities with hover effects
3. **How It Works**: Step-by-step process visualization
4. **Testimonials**: Social proof with customer reviews
5. **CTA Section**: Final conversion push with platform statistics

### 👤 User Management
- **Role-based Registration**: Organizers vs Buyers with different interfaces
- **Secure Authentication**: Password visibility toggle, remember me, social login options
- **Profile Management**: Avatar integration and user preferences

### 📊 Dashboard Analytics
- **Real-time Stats**: Ticket sales, revenue, active events tracking
- **Interactive Charts**: Line charts for trends, bar charts for revenue, doughnut charts for categories
- **Recent Activity**: Live feed of user actions and events
- **Quick Actions**: One-click access to common tasks

### 🎫 Event Management
- **Intuitive Event Creation**: Step-by-step form with validation
- **Multiple Ticket Types**: Regular, VIP, Table with custom pricing
- **Custom Fields**: Dynamic form builder for additional attendee information
- **Image Upload**: Drag-and-drop event image handling
- **Real-time Preview**: Live preview of event page as you build

### 💳 Ticket Purchasing
- **Beautiful Ticket Display**: Premium event page design
- **Quantity Selection**: Intuitive +/- controls with availability tracking
- **Price Calculation**: Real-time total calculation with currency formatting
- **Secure Checkout**: Paystack integration placeholder with security badges

### 💰 Financial Management
- **Earnings Dashboard**: Available, pending, and total earnings tracking
- **Withdrawal System**: Bank details form with validation
- **Transaction History**: Complete withdrawal history with status tracking
- **Fee Transparency**: Clear 10% platform fee communication

### 🛡️ Admin Panel
- **Platform Analytics**: User growth, revenue trends, event categories
- **User Management**: Complete user oversight and management tools
- **Activity Monitoring**: Real-time platform activity feed
- **Quick Actions**: Administrative shortcuts for common tasks

## Component Architecture

### Layouts
Each layout provides a consistent structure for different user types:
- **GuestLayout**: Clean header/footer for public pages
- **AppLayout**: Sidebar navigation for authenticated users
- **TicketLayout**: Minimal header for ticket purchasing
- **AdminLayout**: Dark sidebar for administrative interface

### Reusable Components
- **Modal**: Flexible modal with size options and animations
- **Notifications**: Auto-dismissing alerts with progress bars
- **Header**: Responsive navigation with mobile menu
- **Footer**: Social links and company information

### Animation Patterns
- **Page Transitions**: Smooth fade-in animations on route changes
- **Scroll Animations**: Elements animate into view as user scrolls
- **Hover Effects**: Subtle scale and color transitions
- **Loading States**: Skeleton screens and progress indicators

## Styling Guidelines

### Color Palette
- **Primary**: Indigo (tickget-primary: #4F46E5)
- **Secondary**: Purple gradients for accents
- **Success**: Green for positive actions
- **Warning**: Yellow for cautions
- **Error**: Red for errors
- **Neutral**: Gray scale for text and backgrounds

### Typography
- **Headings**: Bold, clear hierarchy
- **Body Text**: Readable line height and spacing
- **Interactive Elements**: Consistent font weights

### Spacing
- **Consistent Grid**: 4px base unit system
- **Generous Whitespace**: Breathing room between elements
- **Logical Grouping**: Related elements visually connected

## Responsive Breakpoints
- **Mobile**: 0-640px (sm)
- **Tablet**: 641-768px (md)
- **Desktop**: 769-1024px (lg)
- **Large Desktop**: 1025px+ (xl)

## Performance Optimizations
- **Code Splitting**: Automatic route-based splitting with Inertia.js
- **Image Optimization**: Lazy loading and responsive images
- **Animation Performance**: GPU-accelerated transforms
- **Bundle Size**: Tree-shaking and minimal dependencies

## Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Run Laravel server
php artisan serve
```

## Browser Support
- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile**: iOS Safari 14+, Chrome Mobile 90+
- **Progressive Enhancement**: Graceful degradation for older browsers

## Accessibility Features
- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Color Contrast**: WCAG AA compliant contrast ratios
- **Focus Management**: Clear focus indicators

## Future Enhancements
- **Dark Mode**: Theme switching capability
- **Internationalization**: Multi-language support
- **PWA Features**: Offline functionality and push notifications
- **Advanced Analytics**: More detailed reporting and insights
- **Real-time Updates**: WebSocket integration for live updates

## Contributing
1. Follow the established component structure
2. Maintain consistent styling patterns
3. Add proper TypeScript types (when migrating)
4. Include accessibility considerations
5. Test on multiple devices and browsers

## Support
For technical support or questions about the frontend implementation, please refer to the component documentation or create an issue in the project repository.
