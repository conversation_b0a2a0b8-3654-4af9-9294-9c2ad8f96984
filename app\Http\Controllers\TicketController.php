<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use App\Models\Event;
use App\Models\Ticket;
use Inertia\Inertia;

class TicketController extends Controller
{
    /**
     * Display tickets for an event
     */
    public function index(Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        $tickets = $event->tickets()->orderBy('sort_order')->get();

        return Inertia::render('User/EventTickets', [
            'event' => $event,
            'tickets' => $tickets,
            'user' => Auth::user()
        ]);
    }

    /**
     * Store a newly created ticket
     */
    public function store(Request $request, Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'quantity_available' => 'required|integer|min:1',
            'min_purchase' => 'required|integer|min:1',
            'max_purchase' => 'nullable|integer|min:1',
            'sale_start_date' => 'nullable|date',
            'sale_end_date' => 'nullable|date|after_or_equal:sale_start_date',
            'ticket_type' => 'required|string|max:100',
            'benefits' => 'nullable|array',
            'restrictions' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        // Validate max_purchase is not greater than quantity_available
        if ($validated['max_purchase'] && $validated['max_purchase'] > $validated['quantity_available']) {
            throw ValidationException::withMessages([
                'max_purchase' => 'Maximum purchase cannot exceed available quantity.'
            ]);
        }

        // Set sort order
        $validated['sort_order'] = $event->tickets()->max('sort_order') + 1;
        $validated['event_id'] = $event->id;

        $ticket = Ticket::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Ticket created successfully!',
            'ticket' => $ticket
        ]);
    }

    /**
     * Update the specified ticket
     */
    public function update(Request $request, Event $event, Ticket $ticket)
    {
        // Check if user owns this event and ticket belongs to event
        if ($event->organizer_id !== Auth::id() || $ticket->event_id !== $event->id) {
            abort(403, 'Unauthorized access.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'quantity_available' => 'required|integer|min:' . $ticket->quantity_sold,
            'min_purchase' => 'required|integer|min:1',
            'max_purchase' => 'nullable|integer|min:1',
            'sale_start_date' => 'nullable|date',
            'sale_end_date' => 'nullable|date|after_or_equal:sale_start_date',
            'ticket_type' => 'required|string|max:100',
            'benefits' => 'nullable|array',
            'restrictions' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        // Validate max_purchase is not greater than available quantity
        $availableQuantity = $validated['quantity_available'] - $ticket->quantity_sold;
        if ($validated['max_purchase'] && $validated['max_purchase'] > $availableQuantity) {
            throw ValidationException::withMessages([
                'max_purchase' => 'Maximum purchase cannot exceed available quantity.'
            ]);
        }

        $ticket->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Ticket updated successfully!',
            'ticket' => $ticket->fresh()
        ]);
    }

    /**
     * Remove the specified ticket
     */
    public function destroy(Event $event, Ticket $ticket)
    {
        // Check if user owns this event and ticket belongs to event
        if ($event->organizer_id !== Auth::id() || $ticket->event_id !== $event->id) {
            abort(403, 'Unauthorized access.');
        }

        // Check if ticket has been sold
        if ($ticket->quantity_sold > 0) {
            throw ValidationException::withMessages([
                'ticket' => 'Cannot delete ticket that has been sold.'
            ]);
        }

        $ticket->delete();

        return response()->json([
            'success' => true,
            'message' => 'Ticket deleted successfully!'
        ]);
    }

    /**
     * Update ticket sort order
     */
    public function updateOrder(Request $request, Event $event)
    {
        // Check if user owns this event
        if ($event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this event.');
        }

        $validated = $request->validate([
            'tickets' => 'required|array',
            'tickets.*.id' => 'required|exists:tickets,id',
            'tickets.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['tickets'] as $ticketData) {
            Ticket::where('id', $ticketData['id'])
                ->where('event_id', $event->id)
                ->update(['sort_order' => $ticketData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Ticket order updated successfully!'
        ]);
    }
}
