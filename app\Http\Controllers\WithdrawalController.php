<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Illuminate\Validation\ValidationException;
use App\Models\User;
use App\Models\Withdrawal;
use App\Notifications\WithdrawalRequestNotification;
use Inertia\Inertia;

class WithdrawalController extends Controller
{
    /**
     * Display user's withdrawals and available balance
     */
    public function index()
    {
        /** @var User $user */
        $user = Auth::user();

        // Calculate available balance from completed orders
        $totalRevenue = $user->events()
            ->with(['orders' => function ($query) {
                $query->where('status', 'completed');
            }])
            ->get()
            ->sum(function ($event) {
                return $event->getOrganizerRevenue();
            });

        // Calculate total withdrawn amount
        $totalWithdrawn = $user->withdrawals()
            ->whereIn('status', ['approved', 'completed'])
            ->sum('amount');

        // Calculate pending withdrawals
        $pendingWithdrawals = $user->withdrawals()
            ->where('status', 'pending')
            ->sum('amount');

        $availableBalance = $totalRevenue - $totalWithdrawn - $pendingWithdrawals;

        // Get withdrawal history
        $withdrawals = $user->withdrawals()
            ->latest()
            ->paginate(10);

        return Inertia::render('User/Withdraw', [
            'user' => $user,
            'availableBalance' => $availableBalance,
            'totalRevenue' => $totalRevenue,
            'totalWithdrawn' => $totalWithdrawn,
            'pendingWithdrawals' => $pendingWithdrawals,
            'withdrawals' => $withdrawals,
        ]);
    }

    /**
     * Store a new withdrawal request
     */
    public function store(Request $request)
    {
        /** @var User $user */
        $user = Auth::user();

        $validated = $request->validate([
            'amount' => 'required|numeric|min:1000', // Minimum ₦1,000
            'bank_name' => 'required|string|max:255',
            'account_number' => 'required|string|max:20',
            'account_name' => 'required|string|max:255',
            'notes' => 'nullable|string|max:500',
        ]);

        // Calculate available balance
        $totalRevenue = $user->events()
            ->with(['orders' => function ($query) {
                $query->where('status', 'completed');
            }])
            ->get()
            ->sum(function ($event) {
                return $event->getOrganizerRevenue();
            });

        $totalWithdrawn = $user->withdrawals()
            ->whereIn('status', ['approved', 'completed'])
            ->sum('amount');

        $pendingWithdrawals = $user->withdrawals()
            ->where('status', 'pending')
            ->sum('amount');

        $availableBalance = $totalRevenue - $totalWithdrawn - $pendingWithdrawals;

        // Validate withdrawal amount
        if ($validated['amount'] > $availableBalance) {
            throw ValidationException::withMessages([
                'amount' => 'Insufficient balance. Available: ₦' . number_format($availableBalance, 2)
            ]);
        }

        // Create withdrawal request
        $withdrawal = Withdrawal::create([
            'user_id' => $user->id,
            'amount' => $validated['amount'],
            'bank_name' => $validated['bank_name'],
            'account_number' => $validated['account_number'],
            'account_name' => $validated['account_name'],
            'notes' => $validated['notes'],
            'status' => 'pending',
        ]);

        // Update user's bank details for future use
        $user->update([
            'bank_name' => $validated['bank_name'],
            'account_number' => $validated['account_number'],
            'account_name' => $validated['account_name'],
        ]);

        // Send notification to all admins
        $admins = User::where('role', 'admin')->get();
        Notification::send($admins, new WithdrawalRequestNotification($withdrawal));

        return redirect()->route('withdrawals')
            ->with('success', 'Withdrawal request submitted successfully! It will be processed within 1-3 business days.');
    }

    /**
     * Show withdrawal details
     */
    public function show(Withdrawal $withdrawal)
    {
        // Check if user owns this withdrawal
        if ($withdrawal->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this withdrawal.');
        }

        return Inertia::render('User/WithdrawalDetails', [
            'withdrawal' => $withdrawal,
            'user' => Auth::user()
        ]);
    }

    /**
     * Cancel a pending withdrawal
     */
    public function cancel(Withdrawal $withdrawal)
    {
        // Check if user owns this withdrawal
        if ($withdrawal->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this withdrawal.');
        }

        // Can only cancel pending withdrawals
        if (!$withdrawal->isPending()) {
            throw ValidationException::withMessages([
                'withdrawal' => 'Can only cancel pending withdrawals.'
            ]);
        }

        $withdrawal->update([
            'status' => 'cancelled',
            'processed_at' => now(),
            'admin_notes' => 'Cancelled by user'
        ]);

        return redirect()->route('withdrawals')
            ->with('success', 'Withdrawal request cancelled successfully.');
    }
}
