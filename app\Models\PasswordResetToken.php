<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class PasswordResetToken extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'token',
        'expires_at',
        'used'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used' => 'boolean'
    ];

    /**
     * Generate a random 7-character token
     */
    public static function generateToken(): string
    {
        return strtoupper(Str::random(7));
    }

    /**
     * Create a new password reset token
     */
    public static function createToken(string $email): self
    {
        // Delete any existing tokens for this email
        self::where('email', $email)->delete();

        return self::create([
            'email' => $email,
            'token' => self::generateToken(),
            'expires_at' => now()->addMinutes(30), // Token expires in 30 minutes
            'used' => false
        ]);
    }

    /**
     * Verify if token is valid
     */
    public function isValid(): bool
    {
        return !$this->used && $this->expires_at->isFuture();
    }

    /**
     * Mark token as used
     */
    public function markAsUsed(): void
    {
        $this->update(['used' => true]);
    }

    /**
     * Find valid token
     */
    public static function findValidToken(string $email, string $token): ?self
    {
        return self::where('email', $email)
            ->where('token', $token)
            ->where('used', false)
            ->where('expires_at', '>', now())
            ->first();
    }
}
