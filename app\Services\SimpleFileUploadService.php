<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class SimpleFileUploadService
{
    /**
     * Upload a file without image processing (fallback for when GD is not available)
     */
    public function uploadImage(UploadedFile $file, string $directory = 'uploads', array $options = []): string
    {
        // Validate file
        $this->validateImage($file);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        $path = $directory . '/' . $filename;

        try {
            // Store the file directly without processing
            Storage::disk('public')->put($path, file_get_contents($file->getRealPath()));

            Log::info('Image uploaded successfully (no processing)', [
                'original_name' => $file->getClientOriginalName(),
                'stored_path' => $path,
                'file_size' => $file->getSize(),
            ]);

            return $path;

        } catch (\Exception $e) {
            Log::error('Image upload failed', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName(),
            ]);

            throw new \Exception('Failed to upload image: ' . $e->getMessage());
        }
    }

    /**
     * Upload multiple images
     */
    public function uploadMultipleImages(array $files, string $directory = 'uploads', array $options = []): array
    {
        $uploadedPaths = [];

        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $uploadedPaths[] = $this->uploadImage($file, $directory, $options);
            }
        }

        return $uploadedPaths;
    }

    /**
     * Delete an uploaded file
     */
    public function deleteFile(string $path): bool
    {
        try {
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                Log::info('File deleted successfully', ['path' => $path]);
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('File deletion failed', [
                'error' => $e->getMessage(),
                'path' => $path,
            ]);

            return false;
        }
    }

    /**
     * Delete multiple files
     */
    public function deleteMultipleFiles(array $paths): array
    {
        $results = [];

        foreach ($paths as $path) {
            $results[$path] = $this->deleteFile($path);
        }

        return $results;
    }

    /**
     * Validate uploaded image
     */
    protected function validateImage(UploadedFile $file): void
    {
        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            throw new \Exception('File size too large. Maximum allowed size is 5MB.');
        }

        // Check file type
        $allowedMimes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \Exception('Invalid file type. Only JPEG, PNG, JPG, GIF, and WebP images are allowed.');
        }

        // Check if it's actually an image
        if (!getimagesize($file->getRealPath())) {
            throw new \Exception('Invalid image file.');
        }
    }

    /**
     * Generate unique filename
     */
    protected function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d-H-i-s');
        $random = Str::random(8);
        
        return $timestamp . '-' . $random . '.' . $extension;
    }

    /**
     * Get file URL
     */
    public function getFileUrl(string $path): string
    {
        return asset('storage/' . $path);
    }

    /**
     * Get thumbnail URL (same as file URL since no thumbnails are generated)
     */
    public function getThumbnailUrl(string $originalPath): string
    {
        return $this->getFileUrl($originalPath);
    }

    /**
     * Check if file exists
     */
    public function fileExists(string $path): bool
    {
        return Storage::disk('public')->exists($path);
    }

    /**
     * Get file size
     */
    public function getFileSize(string $path): int
    {
        return Storage::disk('public')->size($path);
    }

    /**
     * Get file info
     */
    public function getFileInfo(string $path): array
    {
        if (!$this->fileExists($path)) {
            throw new \Exception('File not found: ' . $path);
        }

        return [
            'path' => $path,
            'url' => $this->getFileUrl($path),
            'thumbnail_url' => $this->getThumbnailUrl($path),
            'size' => $this->getFileSize($path),
            'exists' => true,
        ];
    }
}
