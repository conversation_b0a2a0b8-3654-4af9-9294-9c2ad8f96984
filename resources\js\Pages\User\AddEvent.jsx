import React, { useState, useEffect } from 'react';
import { Head, useForm, router, usePage } from '@inertiajs/react';
import { motion } from 'framer-motion';
import {
  CalendarIcon,
  PhotoIcon,
  XMarkIcon,
  LinkIcon,
  DocumentDuplicateIcon
} from '@heroicons/react/24/outline';
import AppLayout from '../../Layouts/AppLayout';
import ErrorAlert from '../../Components/Notifications/ErrorAlert';
import SuccessAlert from '../../Components/Notifications/SuccessAlert';

const AddEvent = ({ user = {} }) => {
  const { props } = usePage();

  // Inertia form for event creation
  const { data, setData, post, processing, errors } = useForm({
    title: '',
    description: '',
    short_description: '',
    event_date: '',
    event_time: '',
    end_date: '',
    end_time: '',
    venue: '',
    address: '',
    city: '',
    state: '',
    country: 'Nigeria',
    category: '',
    banner_image: null,
    gallery_images: [],
    terms_conditions: '',
    additional_info: '',
    contact_email: user?.email || '',
    contact_phone: user?.phone || '',
    website_url: '',
    social_links: {},
    max_attendees: '',
    requires_approval: false,
    custom_fields: {},
    page_customization: {},
    
  });

  // Local state for UI
  const [tags, setTags] = useState([]);
  const [currentTag, setCurrentTag] = useState('');
  const [bannerPreview, setBannerPreview] = useState(null);
  const [galleryPreviews, setGalleryPreviews] = useState([]);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [createdEventSlug, setCreatedEventSlug] = useState('');
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Event categories
  const eventCategories = [
    'Conference', 'Workshop', 'Seminar', 'Networking', 'Concert', 'Festival',
    'Sports', 'Exhibition', 'Trade Show', 'Business', 'Technology', 'Education',
    'Health & Wellness', 'Food & Drink', 'Arts & Culture', 'Music', 'Comedy',
    'Fashion', 'Gaming', 'Charity', 'Religious', 'Community', 'Other'
  ];

  // Nigerian states
  const nigerianStates = [
    'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', 'Bauchi', 'Bayelsa', 'Benue', 'Borno',
    'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'Gombe', 'Imo',
    'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos',
    'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers',
    'Sokoto', 'Taraba', 'Yobe', 'Zamfara', 'FCT (Abuja)'
  ];

  const [customCategory, setCustomCategory] = useState('');

  // Social links state
  const [socialLinks, setSocialLinks] = useState({
    facebook: '',
    twitter: '',
    instagram: '',
    linkedin: '',
    youtube: '',
    website: ''
  });

  // Ticket types state
  const [ticketTypes, setTicketTypes] = useState([
    {
      id: Date.now(),
      name: 'General Admission',
      description: 'Standard entry to the event',
      price: '0',
      quantity: '',
      is_active: true,
      sort_order: 1
    }
  ]);

  // Check for flash messages on component mount/update
  useEffect(() => {
    if (props.flash) {
      if (props.flash.event_slug) {
        setCreatedEventSlug(props.flash.event_slug);
        setShowSuccessModal(true);
      }

      // Show success notification
      if (props.flash.success) {
        // You can add a toast notification here
        console.log('Success:', props.flash.success);
      }
    }
  }, [props.flash]);

  // Handle form submission
  const handleSubmit = (e, isDraft = false) => {
    e.preventDefault();

    console.log('Form submission started', { isDraft, data, tags, ticketTypes });

    // Validate ticket types
    const validTicketTypes = ticketTypes.filter(ticket =>
      ticket.name.trim() !== '' && ticket.price !== ''
    );

    if (validTicketTypes.length === 0 && !isDraft) {
      setErrorMessage('Please add at least one ticket type with a name and price.');
      setShowErrorAlert(true);
      return;
    }

    try {
      // Create FormData for proper file and data handling
      const formData = new FormData();

      // Add all basic form fields
      Object.keys(data).forEach(key => {
        if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
          if (key === 'banner_image' && data[key] instanceof File) {
            formData.append(key, data[key]);
          } else if (key === 'gallery_images' && Array.isArray(data[key])) {
            data[key].forEach((file, index) => {
              if (file instanceof File) {
                formData.append(`gallery_images[${index}]`, file);
              }
            });
          } else if (typeof data[key] === 'object') {
            formData.append(key, JSON.stringify(data[key]));
          } else {
            formData.append(key, data[key]);
          }
        }
      });

      // Add tags
      if (tags && tags.length > 0) {
        formData.append('tags', JSON.stringify(tags));
        console.log('Adding tags:', tags);
      }

      // Add social links
      const filteredSocialLinks = Object.fromEntries(
        Object.entries(socialLinks).filter(([key, value]) => value.trim() !== '')
      );
      if (Object.keys(filteredSocialLinks).length > 0) {
        formData.append('social_links', JSON.stringify(filteredSocialLinks));
        console.log('Adding social links:', filteredSocialLinks);
      } else {
        // Send empty object to satisfy array validation
        formData.append('social_links', JSON.stringify({}));
      }

      // Add ticket types (only valid ones)
      if (validTicketTypes.length > 0) {
        formData.append('ticket_types', JSON.stringify(validTicketTypes));
        console.log('Adding ticket types:', validTicketTypes);
        console.log('Ticket types JSON:', JSON.stringify(validTicketTypes));
      } else {
        console.log('No valid ticket types found');
      }

      // Add draft status
      formData.append('is_active', isDraft ? 'false' : 'true');

      console.log('Submitting FormData with ticket_types:', formData.get('ticket_types'));

      // Use router.post with FormData for proper handling
      router.post('/events', formData, {
        onSuccess: (page) => {
          console.log('Success response:', page);
          // Handle success - check for event slug in flash data or response
          let eventSlug = 'new-event';

          if (page.props && page.props.flash && page.props.flash.event_slug) {
            eventSlug = page.props.flash.event_slug;
          } else if (page.props && page.props.event && page.props.event.slug) {
            eventSlug = page.props.event.slug;
          }

          setCreatedEventSlug(eventSlug);
          setShowSuccessModal(true);
        },
        onError: (errors) => {
          console.error('Form submission errors:', errors);

          // Show specific error messages
          let errorMsg = 'There was an error submitting the form. Please check the fields and try again.';
          if (typeof errors === 'object') {
            const firstError = Object.values(errors)[0];
            if (Array.isArray(firstError)) {
              errorMsg = firstError[0];
            } else if (typeof firstError === 'string') {
              errorMsg = firstError;
            }
          } else if (typeof errors === 'string') {
            errorMsg = errors;
          }

          setErrorMessage(errorMsg);
          setShowErrorAlert(true);
        },
        onFinish: () => {
          console.log('Form submission finished');
        }
      });

    } catch (error) {
      console.error('Error in handleSubmit:', error);
      setErrorMessage('An unexpected error occurred: ' + error.message);
      setShowErrorAlert(true);
    }
  };

  // Handle file uploads
  const handleBannerUpload = (e) => {
    const file = e.target.files?.[0];
    if (file) {
      console.log('Banner file selected:', file);
      setData('banner_image', file);
      setBannerPreview(URL.createObjectURL(file));
    }
  };

  const handleGalleryUpload = (e) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      console.log('Gallery files selected:', files);
      const currentGallery = data.gallery_images || [];
      setData('gallery_images', [...currentGallery, ...files]);
      const newPreviews = files.map(file => URL.createObjectURL(file));
      setGalleryPreviews([...galleryPreviews, ...newPreviews]);
    }
  };

  // Handle tags
  const handleTagInput = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      const tag = currentTag.trim();
      if (tag && !tags.includes(tag)) {
        setTags([...tags, tag]);
        setCurrentTag('');
      }
    }
  };

  const removeTag = (tagToRemove) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Handle category selection
  const handleCategoryChange = (e) => {
    const value = e?.target?.value || '';
    if (value === 'custom') {
      setData('category', customCategory || '');
    } else {
      setData('category', value);
      setCustomCategory('');
    }
  };

  // Ticket type management functions
  const addTicketType = () => {
    const newTicket = {
      id: Date.now(),
      name: '',
      description: '',
      price: '',
      quantity: '',
      is_active: true,
      sort_order: ticketTypes.length + 1
    };
    setTicketTypes([...ticketTypes, newTicket]);
  };

  const removeTicketType = (id) => {
    if (ticketTypes.length > 1) {
      setTicketTypes(ticketTypes.filter(ticket => ticket.id !== id));
    }
  };

  const updateTicketType = (id, field, value) => {
    setTicketTypes(ticketTypes.map(ticket =>
      ticket.id === id ? { ...ticket, [field]: value } : ticket
    ));
  };

  // Success Modal Component
  const SuccessModal = () => {
    const eventUrl = `${window.location.origin}/event-details/${createdEventSlug || 'new-event'}`;

    const copyToClipboard = async () => {
      try { 
        await navigator.clipboard.writeText(eventUrl);
        setSuccessMessage('Link copied to clipboard!');
        setShowSuccessAlert(true);
      } catch (err) {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = eventUrl;
        document.body.appendChild(textArea);
        textArea.select();
        textArea.focus();
        textArea.setSelectionRange(0, textArea.value.length);
        try {
          document.execCommand('copy');
        } catch (copyErr) {
          console.error('Copy command failed:', copyErr);
        }
        document.body.removeChild(textArea);
        setSuccessMessage('Link copied to clipboard!');
        setShowSuccessAlert(true);
      }
    };

    const goToDashboard = () => {
      window.location.href = '/dashboard';
    };

    return (
      <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
        >
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
              <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Event Created Successfully!</h3>
            <p className="text-sm text-gray-500 mb-4">Your event has been created. Share the link below:</p>
            
            <div className="bg-gray-50 rounded-lg p-3 mb-4">
              <div className="flex items-center space-x-2">
                <LinkIcon className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600 truncate">{eventUrl}</span>
                <button
                  onClick={copyToClipboard}
                  className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600"
                >
                  <DocumentDuplicateIcon className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={copyToClipboard}
                className="flex-1 bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700"
              >
                Copy Link
              </button>
              <button
                onClick={goToDashboard}
                className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700"
              >
                Okay
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    );
  };

  return (
    <AppLayout user={user}>
      <Head title="Create Event - TickGet" />
      
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Success Notification */}
          {props.flash && props.flash.success && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4"
            >
              <div className="flex items-center">
                <svg className="w-5 h-5 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <p className="text-green-800 font-medium">{props.flash.success}</p>
              </div>
            </motion.div>
          )}

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <h1 className="text-3xl font-bold text-gray-900">Create New Event</h1>
            <p className="mt-2 text-gray-600">Fill in the details below to create your event</p>

            {/* Progress Indicator */}
            <div className="mt-6 bg-white rounded-lg p-4 shadow-sm border border-gray-200">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium text-gray-700">Progress</span>
                <span className="text-gray-500">7 sections to complete</span>
              </div>
              <div className="mt-2 bg-gray-200 rounded-full h-2">
                <div className="bg-indigo-600 h-2 rounded-full transition-all duration-300" style={{ width: '0%' }}></div>
              </div>
              <div className="mt-3 grid grid-cols-7 gap-1 text-xs">
                <div className="text-center">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mx-auto mb-1"></div>
                  <span className="text-gray-500">Basic Info</span>
                </div>
                <div className="text-center">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mx-auto mb-1"></div>
                  <span className="text-gray-500">Date & Time</span>
                </div>
                <div className="text-center">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mx-auto mb-1"></div>
                  <span className="text-gray-500">Location</span>
                </div>
                <div className="text-center">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mx-auto mb-1"></div>
                  <span className="text-gray-500">Category</span>
                </div>
                <div className="text-center">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mx-auto mb-1"></div>
                  <span className="text-gray-500">Images</span>
                </div>
                <div className="text-center">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mx-auto mb-1"></div>
                  <span className="text-gray-500">Tickets</span>
                </div>
                <div className="text-center">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mx-auto mb-1"></div>
                  <span className="text-gray-500">Details</span>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.form
            onSubmit={(e) => handleSubmit(e, false)}
            className="space-y-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {/* Basic Information */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <span className="text-indigo-600 font-semibold text-sm">1</span>
                  </div>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-900">Basic Information</h2>
                  <p className="text-sm text-gray-500">Tell us about your event</p>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Title *
                  </label>
                  <input
                    type="text"
                    value={data.title}
                    onChange={(e) => setData('title', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="Enter a compelling event title"
                  />
                  {errors.title && <p className="mt-2 text-sm text-red-600 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.title}
                  </p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Short Description
                  </label>
                  <input
                    type="text"
                    value={data.short_description}
                    onChange={(e) => setData('short_description', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="Brief description for event listings"
                    maxLength="500"
                  />
                  <div className="mt-1 flex justify-between">
                    <div>
                      {errors.short_description && <p className="text-sm text-red-600 flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {errors.short_description}
                      </p>}
                    </div>
                    <span className="text-xs text-gray-400">{data.short_description.length}/500</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Description *
                  </label>
                  <textarea
                    value={data.description}
                    onChange={(e) => setData('description', e.target.value)}
                    rows={5}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 resize-none"
                    placeholder="Provide a detailed description of your event. What can attendees expect?"
                  />
                  {errors.description && <p className="mt-2 text-sm text-red-600 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.description}
                  </p>}
                </div>
              </div>
            </motion.div>

            {/* Date & Time */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <CalendarIcon className="w-4 h-4 text-indigo-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-900">Date & Time</h2>
                  <p className="text-sm text-gray-500">When will your event take place?</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Date *
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      value={data.event_date}
                      onChange={(e) => setData('event_date', e.target.value)}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    />
                  </div>
                  {errors.event_date && <p className="mt-2 text-sm text-red-600 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.event_date}
                  </p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Time *
                  </label>
                  <div className="relative">
                    <input
                      type="time"
                      value={data.event_time}
                      onChange={(e) => setData('event_time', e.target.value)}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    />
                  </div>
                  {errors.event_time && <p className="mt-2 text-sm text-red-600 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.event_time}
                  </p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date <span className="text-gray-400">(Optional)</span>
                  </label>
                  <input
                    type="date"
                    value={data.end_date}
                    onChange={(e) => setData('end_date', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Time <span className="text-gray-400">(Optional)</span>
                  </label>
                  <input
                    type="time"
                    value={data.end_time}
                    onChange={(e) => setData('end_time', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                  />
                </div>
              </div>
            </motion.div>

            {/* Location */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-900">Location</h2>
                  <p className="text-sm text-gray-500">Where will your event take place?</p>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Venue *
                  </label>
                  <input
                    type="text"
                    value={data.venue}
                    onChange={(e) => setData('venue', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="e.g., Eko Convention Centre, Lagos"
                  />
                  {errors.venue && <p className="mt-2 text-sm text-red-600 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.venue}
                  </p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address *
                  </label>
                  <textarea
                    value={data.address}
                    onChange={(e) => setData('address', e.target.value)}
                    rows={3}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 resize-none"
                    placeholder="Full street address including landmarks"
                  />
                  {errors.address && <p className="mt-2 text-sm text-red-600 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.address}
                  </p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      City *
                    </label>
                    <input
                      type="text"
                      value={data.city}
                      onChange={(e) => setData('city', e.target.value)}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="e.g., Lagos"
                    />
                    {errors.city && <p className="mt-2 text-sm text-red-600 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.city}
                    </p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      State *
                    </label>
                    <select
                      value={data.state}
                      onChange={(e) => setData('state', e.target.value)}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      required
                    >
                      <option value="">Select State</option>
                      {nigerianStates.map(state => (
                        <option key={state} value={state}>{state}</option>
                      ))}
                    </select>
                    {errors.state && <p className="mt-2 text-sm text-red-600 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.state}
                    </p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Country
                    </label>
                    <input
                      type="text"
                      value={data.country}
                      onChange={(e) => setData('country', e.target.value)}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="Nigeria"
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Category & Tags */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-900">Category & Tags</h2>
                  <p className="text-sm text-gray-500">Help people discover your event</p>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Category
                  </label>
                  <select
                    value={data.category}
                    onChange={handleCategoryChange}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 bg-white"
                  >
                    <option value="">Select a category</option>
                    {eventCategories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                    <option value="custom">+ Custom Category</option>
                  </select>

                  {data.category === 'custom' && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-3"
                    >
                      <input
                        type="text"
                        value={customCategory}
                        onChange={(e) => {
                          setCustomCategory(e.target.value);
                          setData('category', e.target.value);
                        }}
                        className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                        placeholder="Enter your custom category"
                      />
                    </motion.div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <div className="space-y-3">
                    <input
                      type="text"
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                      onKeyDown={handleTagInput}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="Type tags and press Enter or comma to add (e.g., networking, tech, startup)"
                    />

                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {tags.map((tag, index) => (
                          <motion.span
                            key={index}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 border border-indigo-200"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="ml-2 inline-flex items-center justify-center w-5 h-5 rounded-full text-indigo-500 hover:bg-indigo-200 hover:text-indigo-700 transition-colors duration-200"
                            >
                              <XMarkIcon className="w-3 h-3" />
                            </button>
                          </motion.span>
                        ))}
                      </div>
                    )}

                    <p className="text-xs text-gray-500">
                      Tags help people find your event. Add relevant keywords that describe your event.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Images */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <PhotoIcon className="w-4 h-4 text-indigo-600" />
                  </div>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-900">Event Images</h2>
                  <p className="text-sm text-gray-500">Add attractive visuals to showcase your event</p>
                </div>
              </div>

              <div className="space-y-8">
                {/* Banner Image */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Banner Image <span className="text-gray-400">(Recommended)</span>
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 hover:border-indigo-400 transition-colors duration-200">
                    {bannerPreview ? (
                      <motion.div
                        className="relative"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <img
                          src={bannerPreview}
                          alt="Banner preview"
                          className="w-full h-56 object-cover rounded-lg shadow-md"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setBannerPreview(null);
                            setData('banner_image', null);
                          }}
                          className="absolute top-3 right-3 bg-red-500 text-white rounded-full p-2 hover:bg-red-600 shadow-lg transition-colors duration-200"
                        >
                          <XMarkIcon className="w-4 h-4" />
                        </button>
                        <div className="absolute bottom-3 left-3 bg-black bg-opacity-50 text-white px-3 py-1 rounded-lg text-sm">
                          Banner Image
                        </div>
                      </motion.div>
                    ) : (
                      <div className="text-center">
                        <PhotoIcon className="mx-auto h-16 w-16 text-gray-400 mb-4" />
                        <div className="space-y-2">
                          <label className="cursor-pointer">
                            <span className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200">
                              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                              </svg>
                              Upload Banner Image
                            </span>
                            <input
                              type="file"
                              className="sr-only"
                              accept="image/*"
                              onChange={handleBannerUpload}
                            />
                          </label>
                          <p className="text-sm text-gray-500">
                            PNG, JPG, GIF up to 5MB. Recommended size: 1920x1080px
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                  {errors.banner_image && <p className="mt-3 text-sm text-red-600 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.banner_image}
                  </p>}
                </div>

              {/* Gallery Images */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Gallery Images (Optional)</label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                  <div className="text-center">
                    <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                      <label className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900">
                          Upload gallery images
                        </span>
                        <input
                          type="file"
                          className="sr-only"
                          accept="image/*"
                          multiple
                          onChange={handleGalleryUpload}
                        />
                      </label>
                    </div>
                  </div>
                </div>

                {galleryPreviews.length > 0 && (
                  <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                    {galleryPreviews.map((preview, index) => (
                      <div key={index} className="relative">
                        <img
                          src={preview}
                          alt={`Gallery ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const newPreviews = galleryPreviews.filter((_, i) => i !== index);
                            const newFiles = data.gallery_images.filter((_, i) => i !== index);
                            setGalleryPreviews(newPreviews);
                            setData('gallery_images', newFiles);
                          }}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                        >
                          <XMarkIcon className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            </motion.div>

            {/* Additional Information */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-900">Additional Information</h2>
                  <p className="text-sm text-gray-500">Optional details and settings</p>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Terms & Conditions <span className="text-gray-400">(Optional)</span>
                  </label>
                  <textarea
                    value={data.terms_conditions}
                    onChange={(e) => setData('terms_conditions', e.target.value)}
                    rows={4}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 resize-none"
                    placeholder="Enter any terms and conditions for your event (e.g., refund policy, age restrictions, dress code)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Information <span className="text-gray-400">(Optional)</span>
                  </label>
                  <textarea
                    value={data.additional_info}
                    onChange={(e) => setData('additional_info', e.target.value)}
                    rows={4}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 resize-none"
                    placeholder="Any additional information for attendees (e.g., what to bring, parking info, contact details)"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Maximum Attendees <span className="text-gray-400">(Optional)</span>
                  </label>
                  <input
                    type="number"
                    value={data.max_attendees}
                    onChange={(e) => setData('max_attendees', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="e.g., 100 (leave empty for unlimited)"
                    min="1"
                  />
                  <p className="mt-2 text-sm text-gray-500">
                    Set a limit on the number of people who can attend your event
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Contact Information */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
            >
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-900">Contact Information</h2>
                  <p className="text-sm text-gray-500">How can attendees reach you?</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Email
                  </label>
                  <input
                    type="email"
                    value={data.contact_email}
                    onChange={(e) => setData('contact_email', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Phone <span className="text-gray-400">(Optional)</span>
                  </label>
                  <input
                    type="tel"
                    value={data.contact_phone}
                    onChange={(e) => setData('contact_phone', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="+234 ************"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Website URL <span className="text-gray-400">(Optional)</span>
                  </label>
                  <input
                    type="url"
                    value={data.website_url}
                    onChange={(e) => setData('website_url', e.target.value)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                    placeholder="https://yourevent.com"
                  />
                </div>
              </div>

              {/* Social Links Section */}
              <div className="mt-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Social Media Links <span className="text-gray-400">(Optional)</span></h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Facebook
                    </label>
                    <input
                      type="url"
                      value={socialLinks.facebook}
                      onChange={(e) => setSocialLinks({...socialLinks, facebook: e.target.value})}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="https://facebook.com/yourevent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Twitter
                    </label>
                    <input
                      type="url"
                      value={socialLinks.twitter}
                      onChange={(e) => setSocialLinks({...socialLinks, twitter: e.target.value})}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="https://twitter.com/yourevent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Instagram
                    </label>
                    <input
                      type="url"
                      value={socialLinks.instagram}
                      onChange={(e) => setSocialLinks({...socialLinks, instagram: e.target.value})}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="https://instagram.com/yourevent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      LinkedIn
                    </label>
                    <input
                      type="url"
                      value={socialLinks.linkedin}
                      onChange={(e) => setSocialLinks({...socialLinks, linkedin: e.target.value})}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="https://linkedin.com/company/yourevent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      YouTube
                    </label>
                    <input
                      type="url"
                      value={socialLinks.youtube}
                      onChange={(e) => setSocialLinks({...socialLinks, youtube: e.target.value})}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="https://youtube.com/c/yourevent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Website
                    </label>
                    <input
                      type="url"
                      value={socialLinks.website}
                      onChange={(e) => setSocialLinks({...socialLinks, website: e.target.value})}
                      className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                      placeholder="https://yourevent.com"
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Ticket Types */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.0 }}
            >
              <div className="flex items-center mb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1V7a2 2 0 00-2-2H5zM5 14a2 2 0 00-2 2v3a1 1 0 001 1h1a1 1 0 001-1v-3a2 2 0 00-2-2H5zM16 5a2 2 0 012 2v3a1 1 0 01-1 1h-1a1 1 0 01-1-1V7a2 2 0 012-2h1zM16 14a2 2 0 012 2v3a1 1 0 01-1 1h-1a1 1 0 01-1-1v-3a2 2 0 012-2h1z" />
                    </svg>
                  </div>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-semibold text-gray-900">Ticket Types</h2>
                  <p className="text-sm text-gray-500">Set up different ticket options for your event (at least one required)</p>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <svg className="w-5 h-5 text-blue-600 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                      <h4 className="text-sm font-medium text-blue-900 mb-1">Ticket Pricing Tips</h4>
                      <p className="text-sm text-blue-700">
                        • Set price to ₦0 for free events<br/>
                        • Create multiple tiers (Early Bird, Regular, VIP)<br/>
                        • Use descriptions to explain what's included
                      </p>
                    </div>
                  </div>
                </div>

                {ticketTypes.map((ticket, index) => (
                  <motion.div
                    key={ticket.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-6 bg-gray-50"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium text-gray-900">
                        Ticket Type {index + 1}
                      </h3>
                      {ticketTypes.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeTicketType(ticket.id)}
                          className="text-red-600 hover:text-red-800 p-1"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Ticket Name *
                        </label>
                        <input
                          type="text"
                          value={ticket.name}
                          onChange={(e) => updateTicketType(ticket.id, 'name', e.target.value)}
                          className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                          placeholder="e.g., General Admission, VIP, Early Bird"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Price (₦) *
                        </label>
                        <input
                          type="number"
                          value={ticket.price}
                          onChange={(e) => updateTicketType(ticket.id, 'price', e.target.value)}
                          className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                          placeholder="0"
                          min="0"
                          step="0.01"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Quantity Available
                        </label>
                        <input
                          type="number"
                          value={ticket.quantity}
                          onChange={(e) => updateTicketType(ticket.id, 'quantity', e.target.value)}
                          className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                          placeholder="Enter the number of tickets available for this type"
                          min="1"
                        />
                      </div>

                      <div className="flex items-center">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={ticket.is_active}
                            onChange={(e) => updateTicketType(ticket.id, 'is_active', e.target.checked)}
                            className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                          />
                          <span className="ml-2 text-sm text-gray-700">Active</span>
                        </label>
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Description <span className="text-gray-400">(Optional)</span>
                        </label>
                        <textarea
                          value={ticket.description}
                          onChange={(e) => updateTicketType(ticket.id, 'description', e.target.value)}
                          rows={3}
                          className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 resize-none"
                          placeholder="Describe what's included with this ticket type"
                        />
                      </div>
                    </div>
                  </motion.div>
                ))}

                <button
                  type="button"
                  onClick={addTicketType}
                  className="w-full border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-indigo-400 hover:bg-indigo-50 transition-colors duration-200"
                >
                  <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span className="text-gray-600 font-medium">Add Another Ticket Type</span>
                </button>
              </div>
            </motion.div>

            {/* Form Actions */}
            <motion.div
              className="bg-white shadow-lg rounded-xl p-8 border border-gray-100"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4">
                <button
                  type="button"
                  onClick={(e) => handleSubmit(e, true)}
                  disabled={processing}
                  className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {processing ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z" />
                      </svg>
                      Save as Draft
                    </>
                  )}
                </button>
                <button
                  type="submit"
                  disabled={processing}
                  className="inline-flex items-center justify-center px-8 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                >
                  {processing ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Create Event
                    </>
                  )}
                </button>
              </div>
            </motion.div>
        </motion.form>

          {/* Success Modal */}
          {showSuccessModal && <SuccessModal />}

          {/* Success Alert */}
          <SuccessAlert
            show={showSuccessAlert}
            onClose={() => setShowSuccessAlert(false)}
            title="Success!"
            message={successMessage}
          />

          {/* Error Alert */}
          <ErrorAlert
            show={showErrorAlert}
            onClose={() => setShowErrorAlert(false)}
            title="Error!"
            message={errorMessage}
          />
        </div>
      </div>
    </AppLayout>
  );
};

export default AddEvent;
